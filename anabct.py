#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR BACCARAT OPTIMISÉ - ANABCT.PY
======================================

NOUVEL ANALYSEUR BACCARAT SELON SPÉCIFICATIONS COMPLÈTES
Analyse main par main de 10,000 parties avec ~130-140 métriques
Identification des métriques significatives pour patterns S/O

ARCHITECTURE :
- AnalyseurBaccaratOptimise : Classe principale orchestrant l'analyse
- CalculateurMetriquesEntropiques : Calcul des ~130-140 métriques par main
- AnalyseurSignificanceMetriques : Identification métriques significatives S/O
- GenerateurRapportAnalyse : Génération rapports détaillés

CONTRAINTES RESPECTÉES :
- Pas de nouveaux fichiers Python (exception accordée pour anabct.py)
- Consolidation des dépendances dans classes intégrées
- Optimisation 28GB RAM et 8 CPU cores
- Corrélations désactivées pour performance
- Chargement direct sans analyseur_transitions_index5.py

Auteur: Expert Développeur Senior
Date: 2025-06-25 - Version PROFESSIONNELLE
"""

import json
import os
import math
import numpy as np
from datetime import datetime
from collections import Counter
import gc
from typing import Dict, List, Optional, Any
import pickle
import mmap
import hashlib
from pathlib import Path
import multiprocessing

# ============================================================================
# IMPORTS OPTIMISÉS POUR CHARGEMENT JSON ULTRA-RAPIDE
# ============================================================================

try:
    import orjson
    HAS_ORJSON = True
    print("✅ orjson disponible - parsing ultra-rapide activé")
except ImportError:
    HAS_ORJSON = False
    print("⚠️ orjson non disponible - fallback sur JSON standard")

try:
    import ijson
    HAS_IJSON = True
    print("✅ ijson disponible - streaming activé")
except ImportError:
    HAS_IJSON = False
    print("⚠️ ijson non disponible - pas de streaming")

# ============================================================================
# CLASSE CHARGEUR JSON ULTRA-OPTIMISÉ
# ============================================================================

class ChargeurJSONUltraOptimise:
    """
    Chargeur JSON ultra-optimisé avec cache intelligent
    Basé sur la technique révolutionnaire d'analyse_complete_avec_diff.py

    STRATÉGIE MULTI-NIVEAUX :
    1. Memory Mapping + orjson (ultra-rapide)
    2. Cache pickle par chunks (quasi-instantané)
    3. Fallback automatique (orjson → ijson → JSON standard)
    """

    def __init__(self, dataset_path: str, use_cache: bool = True, use_mmap: bool = True):
        """
        Initialise le chargeur ultra-optimisé

        Args:
            dataset_path: Chemin vers le fichier JSON
            use_cache: Utiliser le système de cache
            use_mmap: Utiliser memory mapping
        """
        self.dataset_path = dataset_path
        self.use_cache = use_cache
        self.use_mmap = use_mmap

        # Configuration cache
        self.cache_dir = Path("cache_anabct")
        self.cache_dir.mkdir(exist_ok=True)

        print(f"🔥 CHARGEUR JSON ULTRA-OPTIMISÉ INITIALISÉ")
        print(f"   📁 Dataset : {dataset_path}")
        print(f"   💾 Cache : {'✅' if use_cache else '❌'}")
        print(f"   🗺️ Memory mapping : {'✅' if use_mmap else '❌'}")

    def charger_dataset_ultra_optimise(self) -> Dict[str, Any]:
        """
        Chargement ultra-optimisé avec cache intelligent

        Returns:
            Dict contenant les données du dataset
        """
        print(f"\n🚀 CHARGEMENT DATASET ULTRA-OPTIMISÉ")
        print("=" * 50)

        if not self.use_cache:
            return self._charger_sans_cache()

        cache_key = self._get_cache_key()
        cache_file = self.cache_dir / f"parsed_data_{cache_key}.pkl"
        index_file = self.cache_dir / f"index_{cache_key}.pkl"

        # Vérifier si le cache existe
        if cache_file.exists() and index_file.exists():
            print("🚀 Cache trouvé - chargement ultra-rapide...")
            return self._charger_depuis_cache(cache_file, index_file)

        print("📊 Première analyse - création du cache optimisé...")
        return self._parser_et_cacher(cache_file, index_file)

    def _get_cache_key(self) -> str:
        """Génère une clé de cache basée sur le fichier"""
        try:
            with open(self.dataset_path, 'rb') as f:
                # Lire les premiers et derniers 1KB pour signature rapide
                debut = f.read(1024)
                f.seek(-1024, 2)
                fin = f.read(1024)
                signature = debut + fin

            return hashlib.md5(signature).hexdigest()
        except Exception:
            # Fallback sur la taille et date de modification
            stat = os.stat(self.dataset_path)
            return hashlib.md5(f"{stat.st_size}_{stat.st_mtime}".encode()).hexdigest()

    def _charger_depuis_cache(self, cache_file: Path, index_file: Path) -> Dict[str, Any]:
        """Chargement depuis cache - quasi-instantané"""

        with open(index_file, 'rb') as f:
            index = pickle.load(f)

        print(f"⚡ Chargement depuis cache : {len(index)} chunks")

        # Chargement par chunks depuis le cache
        parties_data = []
        with open(cache_file, 'rb') as f:
            for i, chunk_info in enumerate(index):
                f.seek(chunk_info['offset'])
                chunk_data = pickle.load(f)
                parties_data.extend(chunk_data)

                if i % 10 == 0:
                    print(f"   ⚡ {len(parties_data):,} parties chargées depuis cache...")

        print(f"✅ Cache chargé : {len(parties_data):,} parties")
        return {'parties': parties_data}

    def _parser_et_cacher(self, cache_file: Path, index_file: Path) -> Dict[str, Any]:
        """Parsing optimisé avec création de cache"""

        if self.use_mmap and HAS_ORJSON:
            return self._parser_avec_mmap_orjson(cache_file, index_file)
        elif HAS_ORJSON:
            return self._parser_avec_orjson(cache_file, index_file)
        elif HAS_IJSON:
            return self._parser_avec_ijson_optimise(cache_file, index_file)
        else:
            return self._parser_avec_json_standard(cache_file, index_file)

    def _parser_avec_mmap_orjson(self, cache_file: Path, index_file: Path) -> Dict[str, Any]:
        """Parsing ultra-rapide avec memory mapping + orjson"""

        print("🔥 Parsing avec memory mapping + orjson (ultra-rapide)...")

        try:
            with open(self.dataset_path, 'rb') as f:
                with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mmapped_file:

                    print("⚡ Parsing orjson en cours...")
                    # Convertir le mmap en bytes pour orjson
                    data_bytes = mmapped_file.read()
                    data = orjson.loads(data_bytes)
                    print("✅ Parsing orjson terminé - création du cache...")

                    # Créer le cache par chunks
                    return self._creer_cache_optimise(data, cache_file, index_file)

        except Exception as e:
            print(f"⚠️ Erreur mmap+orjson, fallback vers orjson standard : {e}")
            return self._parser_avec_orjson(cache_file, index_file)

    def _parser_avec_orjson(self, cache_file: Path, index_file: Path) -> Dict[str, Any]:
        """Parsing rapide avec orjson standard"""

        print("⚡ Parsing avec orjson standard...")

        try:
            with open(self.dataset_path, 'rb') as f:
                data_bytes = f.read()
                data = orjson.loads(data_bytes)
                print("✅ Parsing orjson terminé - création du cache...")

                return self._creer_cache_optimise(data, cache_file, index_file)

        except Exception as e:
            print(f"⚠️ Erreur orjson, fallback vers ijson : {e}")
            return self._parser_avec_ijson_optimise(cache_file, index_file)

    def _parser_avec_ijson_optimise(self, cache_file: Path, index_file: Path) -> Dict[str, Any]:
        """Parsing streaming avec ijson optimisé"""

        print("🔄 Parsing avec ijson streaming...")

        try:
            parties = []
            with open(self.dataset_path, 'rb') as f:
                # Parser streaming des parties
                parser = ijson.items(f, 'parties.item')
                for partie in parser:
                    parties.append(partie)

                    if len(parties) % 1000 == 0:
                        print(f"   📊 {len(parties):,} parties parsées...")

            data = {'parties': parties}
            print("✅ Parsing ijson terminé - création du cache...")
            return self._creer_cache_optimise(data, cache_file, index_file)

        except Exception as e:
            print(f"⚠️ Erreur ijson, fallback vers JSON standard : {e}")
            return self._parser_avec_json_standard(cache_file, index_file)

    def _parser_avec_json_standard(self, cache_file: Path, index_file: Path) -> Dict[str, Any]:
        """Parsing avec JSON standard (fallback)"""

        print("📁 Parsing avec JSON standard (fallback)...")

        try:
            with open(self.dataset_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print("✅ Parsing JSON standard terminé - création du cache...")
                return self._creer_cache_optimise(data, cache_file, index_file)

        except Exception as e:
            print(f"❌ Erreur critique JSON standard : {e}")
            raise

    def _creer_cache_optimise(self, data: Dict[str, Any], cache_file: Path, index_file: Path) -> Dict[str, Any]:
        """Créer le cache optimisé par chunks avec MULTIPROCESSING"""

        parties = data.get('parties', [])
        print(f"📦 Création cache pour {len(parties):,} parties...")

        # Configuration multiprocessing pour 8 cœurs
        max_workers = min(8, multiprocessing.cpu_count())
        print(f"🔥 MULTIPROCESSING : {max_workers} cœurs activés pour cache")

        # Diviser en chunks optimisés pour multiprocessing
        chunk_size = max(1000, len(parties) // (max_workers * 4))  # 4 chunks par cœur
        chunks_info = []

        print(f"📊 Chunks optimisés : {chunk_size:,} parties par chunk")

        with open(cache_file, 'wb') as f:
            for i in range(0, len(parties), chunk_size):
                chunk = parties[i:i + chunk_size]
                offset = f.tell()

                # Sauvegarder le chunk
                pickle.dump(chunk, f)

                # Enregistrer les infos du chunk
                chunks_info.append({
                    'offset': offset,
                    'size': len(chunk),
                    'start_idx': i,
                    'end_idx': min(i + chunk_size, len(parties))
                })

                if i % (chunk_size * 10) == 0:
                    print(f"   💾 {i:,} parties mises en cache...")

        # Sauvegarder l'index des chunks
        with open(index_file, 'wb') as f:
            pickle.dump(chunks_info, f)

        print(f"✅ Cache créé : {len(chunks_info)} chunks")
        print(f"🚀 Prêt pour traitement multiprocessing sur {max_workers} cœurs")
        return data

    def _charger_sans_cache(self) -> Dict[str, Any]:
        """Chargement sans cache pour comparaison"""

        print("📊 Chargement sans cache...")

        if HAS_ORJSON:
            return self._parser_orjson_sans_cache()
        else:
            return self._parser_json_standard_sans_cache()

    def _parser_orjson_sans_cache(self) -> Dict[str, Any]:
        """Parsing orjson sans cache"""

        print("⚡ Parsing orjson sans cache...")

        with open(self.dataset_path, 'rb') as f:
            data_bytes = f.read()
            return orjson.loads(data_bytes)

    def _parser_json_standard_sans_cache(self) -> Dict[str, Any]:
        """Parsing JSON standard sans cache"""

        print("📁 Parsing JSON standard sans cache...")

        with open(self.dataset_path, 'r', encoding='utf-8') as f:
            return json.load(f)

# ============================================================================
# CLASSE PRINCIPALE : ANALYSEUR BACCARAT OPTIMISÉ
# ============================================================================

class AnalyseurBaccaratOptimise:
    """
    CLASSE PRINCIPALE ORCHESTRANT L'ANALYSE COMPLÈTE
    ===============================================
    
    Analyse main par main de 10,000 parties de baccarat
    Calcul de ~130-140 métriques entropiques par main
    Identification des métriques significatives pour patterns S/O
    
    FLUX D'ANALYSE :
    1. Chargement direct du dataset JSON
    2. Boucle externe : parcours des 10,000 parties
    3. Boucle interne : analyse main par main (commence à main 5)
    4. Calcul des métriques entropiques (~130-140 par main)
    5. Alignement temporel : main i → pattern i+1
    6. Analyse de significance des métriques pour S/O
    7. Génération du rapport final
    """
    
    def __init__(self, dataset_path: str):
        """
        Initialise l'analyseur avec le chemin du dataset.
        
        Args:
            dataset_path (str): Chemin vers le fichier JSON du dataset
        """
        self.dataset_path = dataset_path
        self.dataset_json = None
        
        # Composants intégrés
        self.calculateur_metriques = CalculateurMetriquesEntropiques()
        self.analyseur_significance = AnalyseurSignificanceMetriques()
        self.generateur_rapport = GenerateurRapportAnalyse()
        
        # Données d'analyse
        self.donnees_analyse = []
        self.metriques_globales = {}
        self.resultats_significance = {}
        
        print("🚀 ANALYSEUR BACCARAT OPTIMISÉ - ANABCT.PY")
        print("=" * 60)
        print("💾 Configuration : 28GB RAM disponible")
        print("⚡ Optimisations : NumPy + vectorisation + 8 cœurs")
        print("🎯 Objectif : ~130-140 métriques par main")
        print("📊 Analyse : Main par main sans moyennage")
        print("=" * 60)
    
    def charger_dataset_direct(self) -> bool:
        """
        Charge le dataset JSON directement sans dépendances externes.
        
        Returns:
            bool: True si chargement réussi, False sinon
        """
        print(f"\n📂 CHARGEMENT DIRECT DU DATASET")
        print("-" * 40)
        
        if not os.path.exists(self.dataset_path):
            print(f"❌ Dataset non trouvé: {self.dataset_path}")
            return False
        
        # Vérifier la taille du fichier
        size_gb = os.path.getsize(self.dataset_path) / (1024**3)
        print(f"📊 Taille dataset : {size_gb:.2f} GB")
        
        try:
            print("🔄 Chargement JSON en cours...")
            with open(self.dataset_path, 'r', encoding='utf-8') as f:
                self.dataset_json = json.load(f)
            
            # Validation de la structure
            if 'parties' not in self.dataset_json:
                print("❌ Structure JSON invalide : clé 'parties' manquante")
                return False
            
            nb_parties = len(self.dataset_json['parties'])
            print(f"✅ Dataset chargé : {nb_parties:,} parties")
            
            # Validation des métadonnées
            metadata = self.dataset_json.get('metadata', {})
            print(f"📋 Générateur : {metadata.get('generateur', 'N/A')}")
            print(f"📅 Date génération : {metadata.get('date_generation', 'N/A')}")
            print(f"🎲 Hasard crypto : {metadata.get('hasard_cryptographique', 'N/A')}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur chargement dataset : {e}")
            return False

    def charger_dataset_ultra_optimise(self) -> bool:
        """
        Charge le dataset JSON avec la stratégie ultra-optimisée.
        Utilise le ChargeurJSONUltraOptimise avec cache intelligent.

        Returns:
            bool: True si chargement réussi, False sinon
        """
        print(f"\n📂 CHARGEMENT ULTRA-OPTIMISÉ DU DATASET")
        print("-" * 50)

        if not os.path.exists(self.dataset_path):
            print(f"❌ Dataset non trouvé : {self.dataset_path}")
            return False

        # Afficher la taille du fichier
        taille_gb = os.path.getsize(self.dataset_path) / (1024**3)
        print(f"📊 Taille dataset : {taille_gb:.2f} GB")

        try:
            # Initialiser le chargeur ultra-optimisé
            chargeur = ChargeurJSONUltraOptimise(
                dataset_path=self.dataset_path,
                use_cache=True,
                use_mmap=True
            )

            # Chargement avec stratégie révolutionnaire
            self.dataset_json = chargeur.charger_dataset_ultra_optimise()

            # Validation de la structure
            if 'parties' not in self.dataset_json:
                print("❌ Structure JSON invalide : clé 'parties' manquante")
                return False

            nb_parties = len(self.dataset_json['parties'])
            print(f"✅ Dataset chargé : {nb_parties:,} parties")

            # Validation des métadonnées
            metadata = self.dataset_json.get('metadata', {})
            print(f"📋 Générateur : {metadata.get('generateur', 'N/A')}")
            print(f"📅 Date génération : {metadata.get('date_generation', 'N/A')}")
            print(f"🎲 Hasard crypto : {metadata.get('hasard_cryptographique', 'N/A')}")

            return True

        except Exception as e:
            print(f"❌ Erreur chargement dataset : {e}")
            return False

    def executer_analyse_complete(self) -> Dict[str, Any]:
        """
        Exécute l'analyse complète main par main.
        
        Returns:
            Dict[str, Any]: Résultats complets de l'analyse
        """
        print(f"\n🔬 ANALYSE COMPLÈTE MAIN PAR MAIN")
        print("-" * 40)
        
        # Phase 1 : Chargement ultra-optimisé des données
        if not self.charger_dataset_ultra_optimise():
            return {'erreur': 'Échec chargement dataset'}
        
        # Phase 2 : Analyse main par main
        print(f"\n📊 PHASE 2: ANALYSE MAIN PAR MAIN")
        print("-" * 40)
        
        parties = self.dataset_json['parties']
        total_parties = len(parties)
        print(f"📊 Parties à analyser : {total_parties:,}")

        # Configuration multiprocessing pour 8 cœurs
        max_workers = min(8, multiprocessing.cpu_count())
        print(f"🔥 MULTIPROCESSING : {max_workers} cœurs activés")

        # Diviser les parties en chunks pour multiprocessing
        chunk_size = max(100, total_parties // (max_workers * 4))  # 4 chunks par cœur
        chunks = []

        for i in range(0, total_parties, chunk_size):
            chunk = parties[i:i + chunk_size]
            chunks.append({
                'chunk_id': len(chunks),
                'parties': chunk,
                'start_idx': i,
                'end_idx': min(i + chunk_size, total_parties)
            })

        print(f"📦 {len(chunks)} chunks créés pour traitement parallèle")
        print(f"📊 ~{chunk_size:,} parties par chunk")

        # Traitement multiprocessing
        print("🚀 Lancement du traitement parallèle...")

        if len(chunks) > 1:
            self._traiter_chunks_multiprocessing(chunks, max_workers)
        else:
            print("⚠️ Fallback vers traitement séquentiel")
            self._traiter_chunks_sequentiel(chunks)
        
        total_mains = len(self.donnees_analyse)
        print(f"✅ Analyse terminée : {total_parties:,} parties, {total_mains:,} mains")
        print(f"🚀 OPTIMISATION : Chargement ultra-rapide avec cache + mmap + orjson + multiprocessing")
        print(f"💾 PERFORMANCE : 28GB RAM + {max_workers} cœurs exploités pour traitement optimal")

        # Phase 3 : Calcul des métriques globales
        print(f"\n🧮 PHASE 3: CALCUL MÉTRIQUES GLOBALES")
        print("-" * 40)
        self.metriques_globales = self.calculateur_metriques.calculer_metriques_globales(self.donnees_analyse)
        
        # Phase 4 : Analyse de significance
        print(f"\n🎯 PHASE 4: ANALYSE SIGNIFICANCE MÉTRIQUES")
        print("-" * 40)
        self.resultats_significance = self.analyseur_significance.analyser_significance_so(
            self.donnees_analyse, self.metriques_globales
        )
        
        # Phase 5 : Génération du rapport
        print(f"\n📋 PHASE 5: GÉNÉRATION RAPPORT FINAL")
        print("-" * 40)
        rapport_final = self.generateur_rapport.generer_rapport_complet(
            self.donnees_analyse, self.metriques_globales, self.resultats_significance
        )
        
        return {
            'donnees_analyse': self.donnees_analyse,
            'metriques_globales': self.metriques_globales,
            'resultats_significance': self.resultats_significance,
            'rapport_final': rapport_final,
            'statistiques': {
                'parties_traitees': total_parties,
                'mains_analysees': total_mains,
                'metriques_par_main': len(self.metriques_globales) if self.metriques_globales else 0
            }
        }

    def _analyser_partie_complete(self, partie: Dict) -> Dict[str, Any]:
        """
        Analyse une partie complète et calcule toutes les métriques main par main.
        LOGIQUE INSPIRÉE DE analyse_complete_avec_diff.py

        Args:
            partie (Dict): Données de la partie

        Returns:
            Dict[str, Any]: Données d'analyse de la partie
        """
        mains = partie.get('mains', [])
        partie_id = partie.get('partie_number', 0)

        if len(mains) < 6:  # Minimum pour commencer l'analyse à la main 5
            return {'erreur': 'Partie trop courte'}

        # Extraire toutes les séquences INDEX5 et INDEX3
        sequences_index5 = []
        sequences_index3 = []

        for main in mains:
            index5 = main.get('index5_combined', '')
            index3 = main.get('index3_result', '')

            if index5:
                sequences_index5.append(index5)
            if index3:
                sequences_index3.append(index3)

        if len(sequences_index5) < 5 or len(sequences_index3) < 5:
            return {'erreur': 'Séquences insuffisantes'}

        # Calculer l'entropie globale de toute la séquence
        entropie_globale = self._calculer_entropie_shannon(sequences_index5)

        # Calculer les patterns S/O/E pour toute la partie
        patterns_soe = self._calculer_patterns_soe(sequences_index3)

        # Calculer les ratios L4/L5 pour chaque main
        ratios_l4 = []
        ratios_l5 = []

        for i in range(4, len(sequences_index5)):  # Commence à l'index 4 (main 5)
            # Séquence L4 : 4 derniers éléments
            seq_l4 = sequences_index5[max(0, i-3):i+1]
            entropie_l4 = self._calculer_entropie_shannon(seq_l4)
            ratio_l4 = entropie_l4 / entropie_globale if entropie_globale > 0 else 0.0
            ratios_l4.append(ratio_l4)

            # Séquence L5 : 5 derniers éléments
            seq_l5 = sequences_index5[max(0, i-4):i+1]
            entropie_l5 = self._calculer_entropie_shannon(seq_l5)
            ratio_l5 = entropie_l5 / entropie_globale if entropie_globale > 0 else 0.0
            ratios_l5.append(ratio_l5)

        # Calculer les variations DIFF_L4 et DIFF_L5
        diff_l4_variations = self._calculer_variations_ratios(ratios_l4)
        diff_l5_variations = self._calculer_variations_ratios(ratios_l5)

        return {
            'partie_id': partie_id,
            'ratios_l4': ratios_l4,
            'ratios_l5': ratios_l5,
            'patterns_soe': patterns_soe[4:] if len(patterns_soe) > 4 else [],  # Commence à la main 5
            'index3_resultats': sequences_index3[4:] if len(sequences_index3) > 4 else [],  # Commence à la main 5
            'diff_l4_variations': diff_l4_variations,
            'diff_l5_variations': diff_l5_variations,
            'entropie_globale': entropie_globale
        }

    def _calculer_variations_ratios(self, ratios: List[float]) -> List[float]:
        """
        Calcule les variations absolues entre mains consécutives
        LOGIQUE IDENTIQUE À analyseur_transitions_index5.py

        Args:
            ratios: Liste des ratios (L4 ou L5)

        Returns:
            list: Liste des variations absolues |ratio_n - ratio_n-1|
        """
        if len(ratios) < 2:
            return []

        variations = []
        for i in range(1, len(ratios)):
            variation = abs(ratios[i] - ratios[i-1])
            variations.append(variation)

        return variations

    def _analyser_toutes_conditions_avec_diff(self, donnees_analyse: List[Dict]) -> tuple:
        """
        Analyse exhaustive des conditions avec DIFF comme dans analyse_complete_avec_diff.py

        Args:
            donnees_analyse: Liste des données d'analyse avec DIFF

        Returns:
            tuple: (conditions_s, conditions_o) - Conditions significatives trouvées
        """
        print("🔍 ANALYSE EXHAUSTIVE DES CONDITIONS AVEC DIFF")
        print("-" * 50)

        conditions_s = []
        conditions_o = []

        # 1. ANALYSE DIFF PAR TRANCHES DE QUALITÉ
        print("📊 1. ANALYSE DIFF PAR TRANCHES DE QUALITÉ")
        tranches_diff = [
            ("PARFAIT", 0.0, 0.020),
            ("EXCELLENT", 0.020, 0.030),
            ("TRÈS_BON", 0.030, 0.050),
            ("BON", 0.050, 0.080),
            ("MOYEN", 0.080, 0.120),
            ("DOUTEUX", 0.120, 0.200),
            ("MAUVAIS", 0.200, 1.0)
        ]

        for nom_tranche, min_diff, max_diff in tranches_diff:
            donnees_tranche = [d for d in donnees_analyse
                             if min_diff <= d['diff'] < max_diff]

            if len(donnees_tranche) >= 100:  # Minimum pour analyse
                cond_s, cond_o = self._analyser_tranche(donnees_tranche, f"DIFF_{nom_tranche}")
                conditions_s.extend(cond_s)
                conditions_o.extend(cond_o)

        # 2. ANALYSE RATIOS L4 PAR TRANCHES
        print("📊 2. ANALYSE RATIOS L4 PAR TRANCHES")
        tranches_l4 = [
            ("TRÈS_FAIBLE", 0.0, 0.3),
            ("FAIBLE", 0.3, 0.6),
            ("MOYEN", 0.6, 0.9),
            ("ÉLEVÉ", 0.9, 1.2),
            ("TRÈS_ÉLEVÉ", 1.2, 10.0)
        ]

        for nom_tranche, min_l4, max_l4 in tranches_l4:
            donnees_tranche = [d for d in donnees_analyse
                             if min_l4 <= d['ratio_l4'] < max_l4]

            if len(donnees_tranche) >= 100:
                cond_s, cond_o = self._analyser_tranche(donnees_tranche, f"L4_{nom_tranche}")
                conditions_s.extend(cond_s)
                conditions_o.extend(cond_o)

        # 3. ANALYSE RATIOS L5 PAR TRANCHES
        print("📊 3. ANALYSE RATIOS L5 PAR TRANCHES")
        tranches_l5 = [
            ("TRÈS_FAIBLE", 0.0, 0.3),
            ("FAIBLE", 0.3, 0.6),
            ("MOYEN", 0.6, 0.9),
            ("ÉLEVÉ", 0.9, 1.2),
            ("TRÈS_ÉLEVÉ", 1.2, 10.0)
        ]

        for nom_tranche, min_l5, max_l5 in tranches_l5:
            donnees_tranche = [d for d in donnees_analyse
                             if min_l5 <= d['ratio_l5'] < max_l5]

            if len(donnees_tranche) >= 100:
                cond_s, cond_o = self._analyser_tranche(donnees_tranche, f"L5_{nom_tranche}")
                conditions_s.extend(cond_s)
                conditions_o.extend(cond_o)

        print(f"✅ ANALYSE TERMINÉE : {len(conditions_s)} conditions S, {len(conditions_o)} conditions O")

        return conditions_s, conditions_o

    def _analyser_tranche(self, donnees_tranche: List[Dict], nom_condition: str) -> tuple:
        """
        Analyse une tranche de données pour identifier les conditions S/O significatives
        LOGIQUE IDENTIQUE À analyse_complete_avec_diff.py

        Args:
            donnees_tranche: Données de la tranche à analyser
            nom_condition: Nom de la condition analysée

        Returns:
            tuple: (conditions_s, conditions_o) pour cette tranche
        """
        if len(donnees_tranche) < 100:
            return [], []

        # Séparer S et O
        donnees_s = [d for d in donnees_tranche if d['pattern'] == 'S']
        donnees_o = [d for d in donnees_tranche if d['pattern'] == 'O']

        total = len(donnees_s) + len(donnees_o)
        if total == 0:
            return [], []

        pourcentage_s = (len(donnees_s) / total) * 100
        pourcentage_o = (len(donnees_o) / total) * 100

        conditions_s = []
        conditions_o = []

        # SEUIL CRITIQUE : 52% (comme dans analyse_complete_avec_diff.py)
        if pourcentage_s >= 52.0:
            force = "FORTE" if pourcentage_s >= 60 else "MODÉRÉE" if pourcentage_s >= 55 else "FAIBLE"
            conditions_s.append({
                'condition': nom_condition,
                'pourcentage_s': pourcentage_s,
                'nb_donnees_s': len(donnees_s),
                'nb_donnees_total': total,
                'force': force
            })

        if pourcentage_o >= 52.0:
            force = "FORTE" if pourcentage_o >= 60 else "MODÉRÉE" if pourcentage_o >= 55 else "FAIBLE"
            conditions_o.append({
                'condition': nom_condition,
                'pourcentage_o': pourcentage_o,
                'nb_donnees_o': len(donnees_o),
                'nb_donnees_total': total,
                'force': force
            })

        return conditions_s, conditions_o

    def _analyser_main_individuelle(self, partie: Dict, index_main: int) -> Optional[Dict[str, Any]]:
        """
        Analyse une main individuelle et calcule toutes les métriques.
        NOUVELLE LOGIQUE BASÉE SUR analyse_complete_avec_diff.py

        Args:
            partie (Dict): Données de la partie
            index_main (int): Index de la main à analyser (commence à 5)

        Returns:
            Optional[Dict[str, Any]]: Données de la main analysée ou None si erreur
        """
        mains = partie.get('mains', [])

        if index_main >= len(mains) or index_main < 5:
            return None

        # CORRECTION MAJEURE : Extraire TOUTES les séquences INDEX3 de la partie
        # pour calculer les patterns correctement
        sequences_index3_complete = []
        sequences_index5_complete = []

        for main in mains:  # TOUTE la partie
            index5 = main.get('index5_combined', '')
            index3 = main.get('index3_result', '')

            if index5:
                sequences_index5_complete.append(index5)
            if index3:
                sequences_index3_complete.append(index3)

        if len(sequences_index3_complete) < index_main + 1:  # Pas assez de données
            return None

        # Calculer les patterns S/O/E pour TOUTE la partie
        patterns_soe_complete = self._calculer_patterns_soe(sequences_index3_complete)

        # Extraire les séquences jusqu'à cette main pour les calculs d'entropie
        sequences_index5 = sequences_index5_complete[:index_main + 1]
        sequences_index3 = sequences_index3_complete[:index_main + 1]

        if len(sequences_index5) < 5 or len(sequences_index3) < 5:  # Minimum pour L4/L5
            return None

        # Calculer l'entropie globale de toute la séquence
        entropie_globale = self._calculer_entropie_shannon(sequences_index5)

        # Calculer les entropies locales L4 et L5 pour cette main
        # L4 : 4 derniers éléments (incluant la main courante)
        seq_l4 = sequences_index5[max(0, index_main-3):index_main+1]
        entropie_l4 = self._calculer_entropie_shannon(seq_l4)
        ratio_l4 = entropie_l4 / entropie_globale if entropie_globale > 0 else 0.0

        # L5 : 5 derniers éléments (incluant la main courante)
        seq_l5 = sequences_index5[max(0, index_main-4):index_main+1]
        entropie_l5 = self._calculer_entropie_shannon(seq_l5)
        ratio_l5 = entropie_l5 / entropie_globale if entropie_globale > 0 else 0.0

        # Calculer la variable DIFF critique
        diff_coherence = abs(ratio_l4 - ratio_l5)

        # CORRECTION CRITIQUE : Obtenir le pattern pour cette main
        # patterns_soe_complete[i] = pattern de la main i
        pattern = 'E'  # Par défaut
        if len(patterns_soe_complete) > index_main and patterns_soe_complete[index_main] is not None:
            pattern = patterns_soe_complete[index_main]

        # Calculer les métriques supplémentaires
        metriques_supplementaires = self._calculer_metriques_supplementaires(
            sequences_index5, sequences_index3, index_main
        )

        return {
            'entropie_l4': entropie_l4,
            'entropie_l5': entropie_l5,
            'entropie_globale': entropie_globale,
            'ratio_l4': ratio_l4,
            'ratio_l5': ratio_l5,
            'diff': diff_coherence,
            'pattern': pattern,
            'index3': sequences_index3[index_main] if index_main < len(sequences_index3) else '',
            'metriques_supplementaires': metriques_supplementaires
        }

    def _traiter_chunks_multiprocessing(self, chunks: List[Dict], max_workers: int):
        """
        Traitement multiprocessing des chunks de parties sur 8 cœurs
        Technique révolutionnaire adaptée d'analyse_complete_avec_diff.py
        """
        print(f"🚀 Traitement parallèle sur {max_workers} cœurs...")

        try:
            import concurrent.futures

            with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
                # Soumettre les tâches
                futures = {
                    executor.submit(self._traiter_chunk_worker, chunk): chunk['chunk_id']
                    for chunk in chunks
                }

                # Collecter les résultats au fur et à mesure
                completed_chunks = 0
                for future in concurrent.futures.as_completed(futures):
                    chunk_id = futures[future]

                    try:
                        chunk_result = future.result()

                        # Fusionner les résultats
                        if chunk_result and 'donnees_analyse' in chunk_result:
                            self.donnees_analyse.extend(chunk_result['donnees_analyse'])

                        completed_chunks += 1
                        print(f"   ✅ Cœur {chunk_id} terminé ({completed_chunks}/{len(chunks)})")

                    except Exception as e:
                        print(f"   ❌ Erreur cœur {chunk_id} : {e}")
                        raise

        except Exception as e:
            print(f"❌ Erreur multiprocessing : {e}")
            print("🔄 Fallback vers traitement séquentiel...")
            self._traiter_chunks_sequentiel(chunks)

    def _traiter_chunks_sequentiel(self, chunks: List[Dict]):
        """Traitement séquentiel de fallback"""
        print("🔄 Traitement séquentiel...")

        for chunk in chunks:
            chunk_result = self._traiter_chunk_worker(chunk)
            if chunk_result and 'donnees_analyse' in chunk_result:
                self.donnees_analyse.extend(chunk_result['donnees_analyse'])

    def _traiter_chunk_worker(self, chunk: Dict) -> Dict:
        """
        Worker pour traiter un chunk de parties
        Fonction statique pour multiprocessing
        """
        parties = chunk['parties']
        chunk_id = chunk['chunk_id']
        donnees_chunk = []

        print(f"🔄 Chunk {chunk_id}: Traitement de {len(parties):,} parties...")

        for partie in parties:
            partie_id = partie.get('partie_number', 'unknown')
            mains = partie.get('mains', [])

            if len(mains) < 6:  # Minimum pour analyse L4/L5
                continue

            # Analyser chaque main dans cette partie
            for i in range(5, len(mains)):  # Commence à main 5 (index réel)
                donnees_main = self._analyser_main_individuelle(partie, i)

                if donnees_main is not None:
                    donnees_main['partie_id'] = partie_id
                    donnees_main['main'] = i
                    donnees_chunk.append(donnees_main)

        print(f"✅ Chunk {chunk_id}: {len(donnees_chunk):,} mains analysées")

        return {
            'chunk_id': chunk_id,
            'donnees_analyse': donnees_chunk
        }

    def _calculer_entropie_locale(self, sequence: List[str], longueur: int) -> float:
        """
        Calcule l'entropie locale sur une fenêtre glissante.

        Args:
            sequence (List[str]): Séquence des INDEX5
            longueur (int): Longueur de la fenêtre (4 ou 5)

        Returns:
            float: Entropie locale moyenne
        """
        if len(sequence) < longueur:
            return 0.0

        entropies = []

        # Fenêtre glissante
        for i in range(len(sequence) - longueur + 1):
            sous_sequence = sequence[i:i + longueur]
            entropie = self._calculer_entropie_shannon(sous_sequence)
            entropies.append(entropie)

        return np.mean(entropies) if entropies else 0.0

    def _calculer_entropie_shannon(self, sequence: List[str]) -> float:
        """
        Calcule l'entropie de Shannon d'une séquence.

        Args:
            sequence (List[str]): Séquence à analyser

        Returns:
            float: Entropie de Shannon
        """
        if not sequence:
            return 0.0

        # Compter les occurrences
        compteur = Counter(sequence)
        total = len(sequence)

        # Calculer l'entropie
        entropie = 0.0
        for count in compteur.values():
            if count > 0:
                p = count / total
                entropie -= p * math.log2(p)

        return entropie

    def _calculer_patterns_soe(self, index3_resultats: List[str]) -> List[str]:
        """
        Calcule les patterns S (Same), O (Opposite), E (Égalité) pour chaque main
        COPIÉ EXACTEMENT DE analyseur_transitions_index5.py (lignes 6365-6417)

        Args:
            index3_resultats: Liste des résultats INDEX3 (BANKER/PLAYER/TIE)

        Returns:
            list: Liste des patterns S/O/E où patterns[i] = pattern de la main i
        """
        if len(index3_resultats) < 2:
            return []

        patterns = [None]  # patterns[0] = None (pas de pattern pour main 0)
        dernier_non_tie = None

        for i in range(1, len(index3_resultats)):
            resultat_actuel = index3_resultats[i]
            resultat_precedent = index3_resultats[i-1]

            # Si le résultat actuel est TIE
            if resultat_actuel == 'TIE':
                patterns.append('E')  # patterns[i] = pattern de la main i
                continue

            # Si le résultat précédent est TIE, chercher le dernier non-TIE
            if resultat_precedent == 'TIE':
                # Chercher le dernier résultat non-TIE avant la position i-1
                dernier_non_tie = None
                for j in range(i-2, -1, -1):
                    if index3_resultats[j] != 'TIE':
                        dernier_non_tie = index3_resultats[j]
                        break

                # Si aucun résultat non-TIE trouvé, on ne peut pas déterminer le pattern
                if dernier_non_tie is None:
                    patterns.append('--')  # Indéterminé
                    continue

                # Comparer avec le dernier non-TIE
                if resultat_actuel == dernier_non_tie:
                    patterns.append('S')  # Same
                else:
                    patterns.append('O')  # Opposite
            else:
                # Comparaison normale (pas de TIE précédent)
                if resultat_actuel == resultat_precedent:
                    patterns.append('S')  # Same
                else:
                    patterns.append('O')  # Opposite

        return patterns

    def _calculer_metriques_supplementaires(self, sequences_index5: List[str],
                                          sequences_index3: List[str], index_main: int) -> Dict[str, float]:
        """
        Calcule les 130-140 métriques complètes selon specifications_programme_analyseur_baccarat.txt

        Args:
            sequences_index5 (List[str]): Séquences INDEX5 jusqu'à cette main
            sequences_index3 (List[str]): Séquences INDEX3 jusqu'à cette main
            index_main (int): Index de la main courante

        Returns:
            Dict[str, float]: Toutes les métriques supplémentaires calculées
        """
        metriques = {}

        # SECTION 3.2 : MÉTRIQUE LOGARITHMIQUE RÉVOLUTIONNAIRE
        if len(sequences_index5) >= 5:
            entropie_globale = self._calculer_entropie_shannon(sequences_index5)
            ratio_l4 = self._calculer_entropie_shannon(sequences_index5[-4:]) / entropie_globale if entropie_globale > 0 else 0.0
            ratio_l5 = self._calculer_entropie_shannon(sequences_index5[-5:]) / entropie_globale if entropie_globale > 0 else 0.0
            diff_value = abs(ratio_l4 - ratio_l5)
            metriques['prob_continuation_log'] = 0.45 + 0.35 * math.log(diff_value + 0.01)

            # SECTION 3.3 : MÉTRIQUES DÉRIVÉES CALCULÉES (7 métriques)
            metriques['somme_ratios'] = ratio_l4 + ratio_l5
            metriques['diff_ratios'] = diff_value  # Identique à DIFF
            metriques['produit_ratios'] = ratio_l4 * ratio_l5
            metriques['moyenne_ratios'] = (ratio_l4 + ratio_l5) / 2
            metriques['ratio_coherence'] = 1 - diff_value
            metriques['indice_stabilite'] = 1 / (1 + diff_value)
            metriques['ratio_ratios'] = ratio_l4 / ratio_l5 if ratio_l5 != 0 else 0.0

        # SECTION 3.4 : MÉTRIQUES STATISTIQUES - ÉCARTS-TYPES (15+ métriques)
        if len(sequences_index5) >= 10:  # Minimum pour calculs statistiques
            metriques.update(self._calculer_metriques_ecarts_types(sequences_index5, sequences_index3))

        # SECTION 3.5 : MÉTRIQUES D'ENTROPIE AVANCÉES (47+ métriques)
        metriques.update(self._calculer_entropies_avancees(sequences_index5, sequences_index3))

        # SECTION 3.6 : MÉTRIQUES DE SÉQUENCES GLOBALES (25+ métriques)
        metriques.update(self._calculer_metriques_sequences_globales(sequences_index5, sequences_index3, index_main))

        # SECTION 3.7 : MÉTRIQUES SUPPLÉMENTAIRES IDENTIFIÉES (17+ métriques)
        metriques.update(self._calculer_metriques_supplementaires_avancees(sequences_index5, sequences_index3))

        return metriques

    def _calculer_metriques_ecarts_types(self, sequences_index5: List[str], sequences_index3: List[str]) -> Dict[str, float]:
        """
        SECTION 3.4 : MÉTRIQUES STATISTIQUES - ÉCARTS-TYPES (15+ métriques)
        Calcule les écarts-types sur fenêtres glissantes
        """
        metriques = {}

        if len(sequences_index5) < 10:
            return metriques

        # Calculer les ratios L4/L5 pour toute la séquence
        ratios_l4 = []
        ratios_l5 = []
        diffs = []

        entropie_globale = self._calculer_entropie_shannon(sequences_index5)

        for i in range(4, len(sequences_index5)):
            seq_l4 = sequences_index5[max(0, i-3):i+1]
            seq_l5 = sequences_index5[max(0, i-4):i+1]

            ratio_l4 = self._calculer_entropie_shannon(seq_l4) / entropie_globale if entropie_globale > 0 else 0.0
            ratio_l5 = self._calculer_entropie_shannon(seq_l5) / entropie_globale if entropie_globale > 0 else 0.0
            diff = abs(ratio_l4 - ratio_l5)

            ratios_l4.append(ratio_l4)
            ratios_l5.append(ratio_l5)
            diffs.append(diff)

        # Calculer les écarts-types
        if len(ratios_l4) >= 2:
            metriques['std_ratio_l4'] = np.std(ratios_l4)
            metriques['std_ratio_l5'] = np.std(ratios_l5)
            metriques['std_diff'] = np.std(diffs)

            # Écarts-types sur fenêtres glissantes
            if len(ratios_l4) >= 5:
                metriques['std_ratio_l4_fenetre_5'] = np.std(ratios_l4[-5:])
                metriques['std_ratio_l5_fenetre_5'] = np.std(ratios_l5[-5:])
                metriques['std_diff_fenetre_5'] = np.std(diffs[-5:])

            if len(ratios_l4) >= 10:
                metriques['std_ratio_l4_fenetre_10'] = np.std(ratios_l4[-10:])
                metriques['std_ratio_l5_fenetre_10'] = np.std(ratios_l5[-10:])
                metriques['std_diff_fenetre_10'] = np.std(diffs[-10:])

        return metriques

    def _calculer_entropies_avancees(self, sequences_index5: List[str], sequences_index3: List[str]) -> Dict[str, float]:
        """
        SECTION 3.5 : MÉTRIQUES D'ENTROPIE AVANCÉES (47+ métriques)
        Calcule les entropies de Rényi, Tsallis, Hartley, conditionnelles, etc.
        """
        metriques = {}

        if len(sequences_index5) < 5:
            return metriques

        # A. Entropies de base adaptées au modèle local/global
        entropie_globale_shannon = self._calculer_entropie_shannon(sequences_index5)

        if len(sequences_index5) >= 4:
            seq_l4 = sequences_index5[-4:]
            entropie_l4_shannon = self._calculer_entropie_shannon(seq_l4)
            metriques['shannon_l4_global_ratio'] = entropie_l4_shannon / entropie_globale_shannon if entropie_globale_shannon > 0 else 0.0

            # Entropie de Rényi (ordre 2)
            entropie_l4_renyi = self._calculer_entropie_renyi(seq_l4, alpha=2.0)
            entropie_globale_renyi = self._calculer_entropie_renyi(sequences_index5, alpha=2.0)
            metriques['renyi_l4_global_ratio'] = entropie_l4_renyi / entropie_globale_renyi if entropie_globale_renyi > 0 else 0.0

            # Entropie de Tsallis (q=2)
            entropie_l4_tsallis = self._calculer_entropie_tsallis(seq_l4, q=2.0)
            entropie_globale_tsallis = self._calculer_entropie_tsallis(sequences_index5, q=2.0)
            metriques['tsallis_l4_global_ratio'] = entropie_l4_tsallis / entropie_globale_tsallis if entropie_globale_tsallis > 0 else 0.0

            # Entropie de Hartley (max-entropie)
            entropie_l4_hartley = self._calculer_entropie_hartley(seq_l4)
            entropie_globale_hartley = self._calculer_entropie_hartley(sequences_index5)
            metriques['hartley_l4_global_ratio'] = entropie_l4_hartley / entropie_globale_hartley if entropie_globale_hartley > 0 else 0.0

        if len(sequences_index5) >= 5:
            seq_l5 = sequences_index5[-5:]
            entropie_l5_shannon = self._calculer_entropie_shannon(seq_l5)
            metriques['shannon_l5_global_ratio'] = entropie_l5_shannon / entropie_globale_shannon if entropie_globale_shannon > 0 else 0.0

            # Entropie de Rényi L5
            entropie_l5_renyi = self._calculer_entropie_renyi(seq_l5, alpha=2.0)
            entropie_globale_renyi = self._calculer_entropie_renyi(sequences_index5, alpha=2.0)
            metriques['renyi_l5_global_ratio'] = entropie_l5_renyi / entropie_globale_renyi if entropie_globale_renyi > 0 else 0.0

            # Entropie de Tsallis L5
            entropie_l5_tsallis = self._calculer_entropie_tsallis(seq_l5, q=2.0)
            entropie_globale_tsallis = self._calculer_entropie_tsallis(sequences_index5, q=2.0)
            metriques['tsallis_l5_global_ratio'] = entropie_l5_tsallis / entropie_globale_tsallis if entropie_globale_tsallis > 0 else 0.0

            # Entropie de Hartley L5
            entropie_l5_hartley = self._calculer_entropie_hartley(seq_l5)
            entropie_globale_hartley = self._calculer_entropie_hartley(sequences_index5)
            metriques['hartley_l5_global_ratio'] = entropie_l5_hartley / entropie_globale_hartley if entropie_globale_hartley > 0 else 0.0

        # B. Entropies conditionnelles et mutuelles
        if len(sequences_index5) >= 5:
            seq_l4 = sequences_index5[-4:]
            seq_l5 = sequences_index5[-5:]

            metriques['conditional_entropy_l4_l5'] = self._calculer_entropie_conditionnelle(seq_l4, seq_l5)
            metriques['conditional_entropy_l5_l4'] = self._calculer_entropie_conditionnelle(seq_l5, seq_l4)
            metriques['mutual_information_l4_l5'] = self._calculer_information_mutuelle(seq_l4, seq_l5)
            metriques['joint_entropy_l4_l5'] = self._calculer_entropie_jointe(seq_l4, seq_l5)
            metriques['cross_entropy_l4_l5'] = self._calculer_cross_entropy_sequences(seq_l4, seq_l5)

        # C. Divergences et distances
        if len(sequences_index5) >= 5:
            seq_l4 = sequences_index5[-4:]
            seq_l5 = sequences_index5[-5:]

            metriques['kl_divergence_l4_global'] = self._calculer_kl_divergence_sequences(seq_l4, sequences_index5)
            metriques['kl_divergence_l5_global'] = self._calculer_kl_divergence_sequences(seq_l5, sequences_index5)
            metriques['js_divergence_l4_l5'] = self._calculer_js_divergence(seq_l4, seq_l5)
            metriques['hellinger_distance_l4_l5'] = self._calculer_distance_hellinger(seq_l4, seq_l5)
            metriques['bhattacharyya_distance_l4_l5'] = self._calculer_distance_bhattacharyya(seq_l4, seq_l5)

        return metriques

    def _calculer_entropie_renyi(self, sequence: List[str], alpha: float = 2.0) -> float:
        """Calcule l'entropie de Rényi d'ordre alpha."""
        if not sequence or alpha == 1.0:
            return self._calculer_entropie_shannon(sequence)

        compteur = Counter(sequence)
        total = len(sequence)

        if alpha == float('inf'):
            # Entropie min (max probabilité)
            return -math.log2(max(compteur.values()) / total)

        somme = sum((count / total) ** alpha for count in compteur.values())

        if somme <= 0:
            return 0.0

        return (1 / (1 - alpha)) * math.log2(somme)

    def _calculer_entropie_tsallis(self, sequence: List[str], q: float = 2.0) -> float:
        """Calcule l'entropie de Tsallis d'ordre q."""
        if not sequence or q == 1.0:
            return self._calculer_entropie_shannon(sequence)

        compteur = Counter(sequence)
        total = len(sequence)

        somme = sum((count / total) ** q for count in compteur.values())

        return (1 - somme) / (q - 1)

    def _calculer_entropie_hartley(self, sequence: List[str]) -> float:
        """Calcule l'entropie de Hartley (entropie max)."""
        if not sequence:
            return 0.0

        unique_elements = len(set(sequence))
        return math.log2(unique_elements) if unique_elements > 0 else 0.0

    def _calculer_entropie_conditionnelle(self, x_sequence: List[str], y_sequence: List[str]) -> float:
        """Calcule l'entropie conditionnelle H(X|Y)."""
        if not x_sequence or not y_sequence or len(x_sequence) != len(y_sequence):
            return 0.0

        # H(X|Y) = H(X,Y) - H(Y)
        entropie_jointe = self._calculer_entropie_jointe(x_sequence, y_sequence)
        entropie_y = self._calculer_entropie_shannon(y_sequence)

        return entropie_jointe - entropie_y

    def _calculer_information_mutuelle(self, x_sequence: List[str], y_sequence: List[str]) -> float:
        """Calcule l'information mutuelle I(X;Y)."""
        if not x_sequence or not y_sequence or len(x_sequence) != len(y_sequence):
            return 0.0

        # I(X;Y) = H(X) + H(Y) - H(X,Y)
        entropie_x = self._calculer_entropie_shannon(x_sequence)
        entropie_y = self._calculer_entropie_shannon(y_sequence)
        entropie_jointe = self._calculer_entropie_jointe(x_sequence, y_sequence)

        return entropie_x + entropie_y - entropie_jointe

    def _calculer_entropie_jointe(self, x_sequence: List[str], y_sequence: List[str]) -> float:
        """Calcule l'entropie jointe H(X,Y)."""
        if not x_sequence or not y_sequence or len(x_sequence) != len(y_sequence):
            return 0.0

        # Créer les paires (x,y)
        paires = list(zip(x_sequence, y_sequence))
        return self._calculer_entropie_shannon([f"{x}_{y}" for x, y in paires])

    def _calculer_cross_entropy_sequences(self, seq1: List[str], seq2: List[str]) -> float:
        """Calcule la cross-entropy entre deux séquences."""
        if not seq1 or not seq2:
            return 0.0

        # Distribution de seq1
        compteur1 = Counter(seq1)
        total1 = len(seq1)
        prob1 = {k: v / total1 for k, v in compteur1.items()}

        # Distribution de seq2
        compteur2 = Counter(seq2)
        total2 = len(seq2)
        prob2 = {k: v / total2 for k, v in compteur2.items()}

        # Cross-entropy H(p,q) = -Σ p(x) * log(q(x))
        cross_entropy = 0.0
        for x in prob1:
            if x in prob2 and prob2[x] > 0:
                cross_entropy -= prob1[x] * math.log2(prob2[x])
            else:
                # Utiliser un petit epsilon pour éviter log(0)
                cross_entropy -= prob1[x] * math.log2(1e-10)

        return cross_entropy

    def _calculer_kl_divergence_sequences(self, seq1: List[str], seq2: List[str]) -> float:
        """Calcule la divergence KL entre deux séquences."""
        if not seq1 or not seq2:
            return 0.0

        # Distribution de seq1
        compteur1 = Counter(seq1)
        total1 = len(seq1)
        prob1 = {k: v / total1 for k, v in compteur1.items()}

        # Distribution de seq2
        compteur2 = Counter(seq2)
        total2 = len(seq2)
        prob2 = {k: v / total2 for k, v in compteur2.items()}

        # KL(P||Q) = Σ P(x) * log(P(x)/Q(x))
        kl_div = 0.0
        for x in prob1:
            if x in prob2 and prob2[x] > 0:
                kl_div += prob1[x] * math.log2(prob1[x] / prob2[x])
            else:
                # Si Q(x) = 0 mais P(x) > 0, KL divergence est infinie
                # On utilise une valeur élevée
                kl_div += prob1[x] * 10.0

        return kl_div

    def _calculer_js_divergence(self, seq1: List[str], seq2: List[str]) -> float:
        """Calcule la divergence Jensen-Shannon entre deux séquences."""
        if not seq1 or not seq2:
            return 0.0

        # Distribution de seq1
        compteur1 = Counter(seq1)
        total1 = len(seq1)
        prob1 = {k: v / total1 for k, v in compteur1.items()}

        # Distribution de seq2
        compteur2 = Counter(seq2)
        total2 = len(seq2)
        prob2 = {k: v / total2 for k, v in compteur2.items()}

        # Distribution moyenne M = (P + Q) / 2
        all_keys = set(prob1.keys()) | set(prob2.keys())
        prob_m = {}
        for k in all_keys:
            p_k = prob1.get(k, 0.0)
            q_k = prob2.get(k, 0.0)
            prob_m[k] = (p_k + q_k) / 2

        # JS(P,Q) = 0.5 * KL(P||M) + 0.5 * KL(Q||M)
        kl_pm = sum(prob1.get(k, 0.0) * math.log2(prob1.get(k, 0.0) / prob_m[k])
                   for k in all_keys if prob1.get(k, 0.0) > 0 and prob_m[k] > 0)

        kl_qm = sum(prob2.get(k, 0.0) * math.log2(prob2.get(k, 0.0) / prob_m[k])
                   for k in all_keys if prob2.get(k, 0.0) > 0 and prob_m[k] > 0)

        return 0.5 * kl_pm + 0.5 * kl_qm

    def _calculer_distance_hellinger(self, seq1: List[str], seq2: List[str]) -> float:
        """Calcule la distance de Hellinger entre deux séquences."""
        if not seq1 or not seq2:
            return 1.0

        # Distribution de seq1
        compteur1 = Counter(seq1)
        total1 = len(seq1)
        prob1 = {k: v / total1 for k, v in compteur1.items()}

        # Distribution de seq2
        compteur2 = Counter(seq2)
        total2 = len(seq2)
        prob2 = {k: v / total2 for k, v in compteur2.items()}

        # Distance de Hellinger H(P,Q) = sqrt(0.5 * Σ (sqrt(P(x)) - sqrt(Q(x)))²)
        all_keys = set(prob1.keys()) | set(prob2.keys())
        somme = 0.0
        for k in all_keys:
            p_k = prob1.get(k, 0.0)
            q_k = prob2.get(k, 0.0)
            somme += (math.sqrt(p_k) - math.sqrt(q_k)) ** 2

        return math.sqrt(0.5 * somme)

    def _calculer_distance_bhattacharyya(self, seq1: List[str], seq2: List[str]) -> float:
        """Calcule la distance de Bhattacharyya entre deux séquences."""
        if not seq1 or not seq2:
            return float('inf')

        # Distribution de seq1
        compteur1 = Counter(seq1)
        total1 = len(seq1)
        prob1 = {k: v / total1 for k, v in compteur1.items()}

        # Distribution de seq2
        compteur2 = Counter(seq2)
        total2 = len(seq2)
        prob2 = {k: v / total2 for k, v in compteur2.items()}

        # Coefficient de Bhattacharyya BC(P,Q) = Σ sqrt(P(x) * Q(x))
        all_keys = set(prob1.keys()) | set(prob2.keys())
        bc = sum(math.sqrt(prob1.get(k, 0.0) * prob2.get(k, 0.0)) for k in all_keys)

        # Distance de Bhattacharyya D_B(P,Q) = -ln(BC(P,Q))
        if bc <= 0:
            return float('inf')

        return -math.log(bc)

    def _calculer_metriques_sequences_globales(self, sequences_index5: List[str],
                                             sequences_index3: List[str], index_main: int) -> Dict[str, float]:
        """
        SECTION 3.6 : MÉTRIQUES DE SÉQUENCES GLOBALES (25+ métriques)
        Calcule les métriques d'entropie métrique, évolutive, Markov, fenêtres glissantes, ergodique
        """
        metriques = {}

        if len(sequences_index5) < 5:
            return metriques

        # A. Entropie métrique et évolutive
        longueur = len(sequences_index5)
        entropie_globale = self._calculer_entropie_shannon(sequences_index5)
        metriques['entropie_metrique'] = entropie_globale / longueur if longueur > 0 else 0.0

        # Entropies conditionnelles de différents ordres
        if longueur >= 3:
            # H(X_n | X_{n-2}, X_{n-1})
            metriques['entropie_conditionnelle_courte'] = self._calculer_entropie_conditionnelle_ordre(sequences_index5, 2)

        if longueur >= 5:
            # H(X_n | X_{n-4},...,X_{n-1})
            metriques['entropie_conditionnelle_moyenne'] = self._calculer_entropie_conditionnelle_ordre(sequences_index5, 4)

        if longueur >= 10:
            # H(X_n | X_{n-9},...,X_{n-1})
            metriques['entropie_conditionnelle_longue'] = self._calculer_entropie_conditionnelle_ordre(sequences_index5, 9)

            # Réduction d'incertitude par la mémoire
            entropie_sans_memoire = self._calculer_entropie_shannon([sequences_index5[-1]])
            entropie_avec_memoire = metriques['entropie_conditionnelle_longue']
            metriques['reduction_incertitude_memoire'] = entropie_sans_memoire - entropie_avec_memoire

        # B. Entropie Markov adaptative
        if longueur >= 3:
            metriques['entropie_markov_ordre1'] = self._calculer_entropie_markov(sequences_index5, ordre=1)

        if longueur >= 4:
            metriques['entropie_markov_ordre2'] = self._calculer_entropie_markov(sequences_index5, ordre=2)

        # C. Fenêtres glissantes évolutives
        if longueur >= 5:
            fenetre_5 = sequences_index5[-5:]
            metriques['entropie_fenetre_5'] = self._calculer_entropie_shannon(fenetre_5)
            metriques['densite_info_fenetre_5'] = metriques['entropie_fenetre_5'] / 5
            metriques['ratio_local_global_5'] = metriques['densite_info_fenetre_5'] / (entropie_globale / longueur) if entropie_globale > 0 else 0.0

        if longueur >= 10:
            fenetre_10 = sequences_index5[-10:]
            metriques['entropie_fenetre_10'] = self._calculer_entropie_shannon(fenetre_10)
            metriques['densite_info_fenetre_10'] = metriques['entropie_fenetre_10'] / 10
            metriques['ratio_local_global_10'] = metriques['densite_info_fenetre_10'] / (entropie_globale / longueur) if entropie_globale > 0 else 0.0

            # Gradient entropique
            if 'entropie_fenetre_5' in metriques:
                metriques['gradient_entropique_5_10'] = metriques['entropie_fenetre_10'] - metriques['entropie_fenetre_5']

        if longueur >= 20:
            fenetre_20 = sequences_index5[-20:]
            metriques['entropie_fenetre_20'] = self._calculer_entropie_shannon(fenetre_20)
            metriques['densite_info_fenetre_20'] = metriques['entropie_fenetre_20'] / 20
            metriques['ratio_local_global_20'] = metriques['densite_info_fenetre_20'] / (entropie_globale / longueur) if entropie_globale > 0 else 0.0

            # Gradient entropique
            if 'entropie_fenetre_10' in metriques:
                metriques['gradient_entropique_10_20'] = metriques['entropie_fenetre_20'] - metriques['entropie_fenetre_10']

        # D. Entropie ergodique
        if longueur >= 10:
            metriques.update(self._calculer_metriques_ergodiques(sequences_index5))

        return metriques

    def _calculer_entropie_conditionnelle_ordre(self, sequence: List[str], ordre: int) -> float:
        """Calcule l'entropie conditionnelle d'ordre donné."""
        if len(sequence) <= ordre:
            return 0.0

        # Créer les contextes et les prédictions
        contextes = []
        predictions = []

        for i in range(ordre, len(sequence)):
            contexte = tuple(sequence[i-ordre:i])
            prediction = sequence[i]
            contextes.append(contexte)
            predictions.append(prediction)

        if not contextes:
            return 0.0

        # Calculer H(X_n | contexte)
        contexte_counts = Counter(contextes)
        total_contextes = len(contextes)

        entropie_conditionnelle = 0.0

        for contexte, count_contexte in contexte_counts.items():
            prob_contexte = count_contexte / total_contextes

            # Trouver toutes les prédictions pour ce contexte
            predictions_pour_contexte = [predictions[i] for i, ctx in enumerate(contextes) if ctx == contexte]

            if predictions_pour_contexte:
                entropie_locale = self._calculer_entropie_shannon(predictions_pour_contexte)
                entropie_conditionnelle += prob_contexte * entropie_locale

        return entropie_conditionnelle

    def _calculer_entropie_markov(self, sequence: List[str], ordre: int) -> float:
        """Calcule l'entropie des transitions de Markov d'ordre donné."""
        if len(sequence) <= ordre:
            return 0.0

        # Créer les transitions
        transitions = []
        for i in range(ordre, len(sequence)):
            etat_precedent = tuple(sequence[i-ordre:i])
            etat_suivant = sequence[i]
            transitions.append((etat_precedent, etat_suivant))

        if not transitions:
            return 0.0

        # Calculer l'entropie des transitions
        return self._calculer_entropie_shannon([f"{prev}_{next}" for prev, next in transitions])

    def _calculer_metriques_ergodiques(self, sequence: List[str]) -> Dict[str, float]:
        """Calcule les métriques d'entropie ergodique."""
        metriques = {}

        if len(sequence) < 10:
            return metriques

        # Entropie ergodique estimée (basée sur fréquences empiriques)
        compteur = Counter(sequence)
        total = len(sequence)

        # Estimation de l'entropie ergodique
        entropie_empirique = self._calculer_entropie_shannon(sequence)
        metriques['entropie_ergodique_estimee'] = entropie_empirique

        # Convergence ergodique (stabilité sur différentes fenêtres)
        if len(sequence) >= 20:
            entropie_premiere_moitie = self._calculer_entropie_shannon(sequence[:len(sequence)//2])
            entropie_seconde_moitie = self._calculer_entropie_shannon(sequence[len(sequence)//2:])
            metriques['convergence_ergodique'] = abs(entropie_premiere_moitie - entropie_seconde_moitie)

        # Écart avec entropie théorique uniforme
        nb_etats_uniques = len(set(sequence))
        entropie_uniforme = math.log2(nb_etats_uniques) if nb_etats_uniques > 0 else 0.0
        metriques['ecart_entropie_theorique'] = abs(entropie_empirique - entropie_uniforme)
        metriques['ecart_ergodique_uniforme'] = abs(entropie_empirique - entropie_uniforme)
        metriques['ratio_ergodique_uniforme'] = entropie_empirique / entropie_uniforme if entropie_uniforme > 0 else 0.0

        # Efficacité ergodique normalisée
        metriques['efficacite_ergodique'] = entropie_empirique / entropie_uniforme if entropie_uniforme > 0 else 0.0

        return metriques

    def _calculer_metriques_supplementaires_avancees(self, sequences_index5: List[str],
                                                    sequences_index3: List[str]) -> Dict[str, float]:
        """
        SECTION 3.7 : MÉTRIQUES SUPPLÉMENTAIRES IDENTIFIÉES (17+ métriques)
        Calcule les métriques dérivées supplémentaires, scores composites, métriques log/exp
        """
        metriques = {}

        if len(sequences_index5) < 5:
            return metriques

        # Calculer les valeurs de base nécessaires
        entropie_globale = self._calculer_entropie_shannon(sequences_index5)
        ratio_l4 = self._calculer_entropie_shannon(sequences_index5[-4:]) / entropie_globale if entropie_globale > 0 else 0.0
        ratio_l5 = self._calculer_entropie_shannon(sequences_index5[-5:]) / entropie_globale if entropie_globale > 0 else 0.0
        diff = abs(ratio_l4 - ratio_l5)

        # Calculer les variations (diff_l4, diff_l5) si on a assez d'historique
        diff_l4 = 0.0
        diff_l5 = 0.0

        if len(sequences_index5) >= 6:
            # Calculer ratio_l4 précédent
            entropie_globale_prev = self._calculer_entropie_shannon(sequences_index5[:-1])
            ratio_l4_prev = self._calculer_entropie_shannon(sequences_index5[-5:-1]) / entropie_globale_prev if entropie_globale_prev > 0 else 0.0
            diff_l4 = ratio_l4 - ratio_l4_prev

        if len(sequences_index5) >= 7:
            # Calculer ratio_l5 précédent
            entropie_globale_prev = self._calculer_entropie_shannon(sequences_index5[:-1])
            ratio_l5_prev = self._calculer_entropie_shannon(sequences_index5[-6:-1]) / entropie_globale_prev if entropie_globale_prev > 0 else 0.0
            diff_l5 = ratio_l5 - ratio_l5_prev

        # A. Métriques dérivées supplémentaires
        metriques['somme_diffs'] = diff_l4 + diff_l5
        metriques['diff_diffs'] = abs(diff_l4 - diff_l5)
        metriques['produit_diffs'] = diff_l4 * diff_l5
        metriques['moyenne_diffs'] = (diff_l4 + diff_l5) / 2
        metriques['ratio_diff_l4_l5'] = diff_l4 / diff_l5 if diff_l5 != 0 else 0.0
        metriques['coherence_diffs'] = 1 - abs(diff_l4 - diff_l5)
        metriques['stabilite_diffs'] = 1 / (1 + abs(diff_l4 - diff_l5))
        metriques['amplitude_variation'] = max(diff_l4, diff_l5) - min(diff_l4, diff_l5)

        # B. Scores composites
        # Score de continuation basé sur DIFF, ratio_l4, ratio_l5
        metriques['score_continuation'] = (1 - diff) * ratio_l4 * (1 + ratio_l5)

        # Score d'alternance basé sur DIFF et écarts
        metriques['score_alternance'] = diff * (1 - ratio_l4) * (1 - ratio_l5)

        # Indice de cohérence global
        coherence_ratios = 1 - abs(ratio_l4 - ratio_l5)
        coherence_diffs = 1 - abs(diff_l4 - diff_l5)
        metriques['indice_coherence_global'] = (coherence_ratios + coherence_diffs) / 2

        # Qualité du signal
        prob_s = 0.45 + 0.35 * math.log(diff + 0.01)  # Formule logarithmique révolutionnaire
        force_coherence = abs(ratio_l4 / ratio_l5 - 1.0) if ratio_l5 != 0 and abs(ratio_l4 - ratio_l5) < 0.1 else 0.0
        metriques['qualite_signal'] = prob_s * (1 + force_coherence)
        metriques['force_coherence'] = force_coherence

        # D. Métriques logarithmiques et exponentielles
        metriques['log_ratio_l4'] = math.log(ratio_l4 + 1e-10)
        metriques['log_ratio_l5'] = math.log(ratio_l5 + 1e-10)
        metriques['log_diff'] = math.log(diff + 1e-10)
        metriques['exp_ratio_l4'] = math.exp(-ratio_l4)
        metriques['exp_ratio_l5'] = math.exp(-ratio_l5)
        metriques['exp_diff'] = math.exp(-diff)

        # Métriques de diversité et concentration
        metriques['longueur_sequence'] = len(sequences_index5)
        metriques['diversite_index5'] = len(set(sequences_index5))
        metriques['diversite_index3'] = len(set(sequences_index3))

        # Métriques de concentration
        if sequences_index5:
            compteur_index5 = Counter(sequences_index5)
            total = len(sequences_index5)
            metriques['concentration_max'] = max(compteur_index5.values()) / total
            metriques['uniformite'] = len(compteur_index5) / total if total > 0 else 0.0

        return metriques


# ============================================================================
# CLASSE : CALCULATEUR MÉTRIQUES ENTROPIQUES
# ============================================================================

class CalculateurMetriquesEntropiques:
    """
    CALCULATEUR DE ~130-140 MÉTRIQUES ENTROPIQUES PAR MAIN
    =====================================================

    Intègre toutes les formules d'entropie du système original :
    - 52+ formules mathématiques d'entropie
    - Métriques dérivées et combinaisons
    - Analyses temporelles et corrélations
    - Optimisations pour performance (corrélations désactivées)

    FORMULES INTÉGRÉES :
    1. Entropies de base (Shannon, Bernoulli, Uniforme)
    2. Divergences (KL, Cross-entropy, JS)
    3. Entropies conditionnelles et jointes
    4. Métriques spécialisées baccarat
    5. Analyses temporelles (Markov, ergodique)
    6. Information mutuelle
    """

    def __init__(self):
        """Initialise le calculateur de métriques."""
        self.metriques_calculees = {}
        self.formules_entropie = {}

    def calculer_metriques_globales(self, donnees_analyse: List[Dict]) -> Dict[str, Any]:
        """
        Calcule toutes les métriques globales sur l'ensemble des données.

        Args:
            donnees_analyse (List[Dict]): Données d'analyse de toutes les mains

        Returns:
            Dict[str, Any]: Métriques globales calculées
        """
        print("   🧮 Calcul des métriques entropiques globales...")

        if not donnees_analyse:
            return {}

        # Extraire les séquences pour calculs
        sequences = self._extraire_sequences_pour_entropie(donnees_analyse)

        # Calculer toutes les formules d'entropie
        self._calculer_toutes_formules_entropie(sequences)

        # Calculer les métriques dérivées
        self._calculer_metriques_derivees(donnees_analyse)

        # Calculer les écarts-types
        self._calculer_ecarts_types(donnees_analyse)

        print(f"   ✅ {len(self.metriques_calculees)} métriques calculées")

        return self.metriques_calculees

    def _extraire_sequences_pour_entropie(self, donnees_analyse: List[Dict]) -> Dict[str, List]:
        """
        Extrait les séquences nécessaires pour les calculs d'entropie.

        Args:
            donnees_analyse (List[Dict]): Données d'analyse

        Returns:
            Dict[str, List]: Séquences extraites
        """
        sequences = {
            'patterns': [],           # Patterns S/O
            'resultats': [],         # Résultats INDEX3
            'ratios_l4': [],         # Ratios L4
            'ratios_l5': [],         # Ratios L5
            'diff_values': [],       # Valeurs DIFF
            'entropies_l4': [],      # Entropies locales L4
            'entropies_l5': [],      # Entropies locales L5
            'entropies_globales': [] # Entropies globales
        }

        for donnee in donnees_analyse:
            # Patterns S/O (convertis en 0/1 pour Bernoulli)
            pattern = donnee.get('pattern', '')
            if pattern == 'S':
                sequences['patterns'].append(1)
            elif pattern == 'O':
                sequences['patterns'].append(0)

            # Autres métriques
            sequences['resultats'].append(donnee.get('index3', ''))
            sequences['ratios_l4'].append(donnee.get('ratio_l4', 0.0))
            sequences['ratios_l5'].append(donnee.get('ratio_l5', 0.0))
            sequences['diff_values'].append(donnee.get('diff', 0.0))
            sequences['entropies_l4'].append(donnee.get('entropie_l4', 0.0))
            sequences['entropies_l5'].append(donnee.get('entropie_l5', 0.0))
            sequences['entropies_globales'].append(donnee.get('entropie_globale', 0.0))

        return sequences

    def _calculer_toutes_formules_entropie(self, sequences: Dict[str, List]):
        """
        Calcule toutes les 52+ formules d'entropie.

        Args:
            sequences (Dict[str, List]): Séquences extraites
        """
        # 1. ENTROPIES DE BASE
        self._calculer_entropies_base(sequences)

        # 2. DIVERGENCES ET COMPARAISONS
        self._calculer_divergences(sequences)

        # 3. ENTROPIES CONDITIONNELLES
        self._calculer_entropies_conditionnelles(sequences)

        # 4. MÉTRIQUES SPÉCIALISÉES BACCARAT
        self._calculer_metriques_baccarat(sequences)

        # 5. ANALYSES TEMPORELLES
        self._calculer_analyses_temporelles(sequences)

        # 6. INFORMATION MUTUELLE
        self._calculer_information_mutuelle(sequences)

    def _calculer_entropies_base(self, sequences: Dict[str, List]):
        """Calcule les entropies de base (Shannon, Bernoulli, Uniforme)."""

        # 1. ENTROPIE DE SHANNON pour patterns S/O
        if sequences['patterns']:
            pattern_counts = Counter(sequences['patterns'])
            total_patterns = len(sequences['patterns'])
            if total_patterns > 0:
                shannon_entropy = 0.0
                for count in pattern_counts.values():
                    if count > 0:
                        p = count / total_patterns
                        shannon_entropy -= p * math.log2(p)
                self.metriques_calculees['shannon_entropy_patterns'] = shannon_entropy

        # 2. ENTROPIE BERNOULLI (distribution binaire S/O)
        if sequences['patterns']:
            p_s = sequences['patterns'].count(1) / len(sequences['patterns'])
            if 0 < p_s < 1:
                bernoulli_entropy = -p_s * math.log2(p_s) - (1-p_s) * math.log2(1-p_s)
                self.metriques_calculees['bernoulli_entropy'] = bernoulli_entropy

        # 3. ENTROPIE UNIFORME pour résultats B/P/T
        if sequences['resultats']:
            resultats_counts = Counter(sequences['resultats'])
            total_resultats = len(sequences['resultats'])
            if total_resultats > 0:
                uniform_entropy = 0.0
                for count in resultats_counts.values():
                    if count > 0:
                        p = count / total_resultats
                        uniform_entropy -= p * math.log2(p)
                self.metriques_calculees['uniform_entropy_resultats'] = uniform_entropy

        # 4. ENTROPIES MOYENNES des métriques continues
        if sequences['ratios_l4']:
            self.metriques_calculees['moyenne_ratios_l4'] = np.mean(sequences['ratios_l4'])
            self.metriques_calculees['variance_ratios_l4'] = np.var(sequences['ratios_l4'])

        if sequences['ratios_l5']:
            self.metriques_calculees['moyenne_ratios_l5'] = np.mean(sequences['ratios_l5'])
            self.metriques_calculees['variance_ratios_l5'] = np.var(sequences['ratios_l5'])

        if sequences['diff_values']:
            self.metriques_calculees['moyenne_diff'] = np.mean(sequences['diff_values'])
            self.metriques_calculees['variance_diff'] = np.var(sequences['diff_values'])

    def _calculer_divergences(self, sequences: Dict[str, List]):
        """Calcule les divergences et comparaisons."""

        # 1. DIVERGENCE KL entre ratios L4 et L5
        if sequences['ratios_l4'] and sequences['ratios_l5']:
            # Discrétiser les ratios pour calcul KL
            ratios_l4_disc = self._discretiser_valeurs(sequences['ratios_l4'])
            ratios_l5_disc = self._discretiser_valeurs(sequences['ratios_l5'])

            if ratios_l4_disc and ratios_l5_disc:
                kl_div = self._calculer_divergence_kl(ratios_l4_disc, ratios_l5_disc)
                self.metriques_calculees['kl_divergence_l4_l5'] = kl_div

        # 2. CROSS-ENTROPY entre patterns et prédictions
        if sequences['patterns'] and sequences['diff_values']:
            # Utiliser DIFF comme prédicteur de patterns
            cross_entropy = self._calculer_cross_entropy(sequences['patterns'], sequences['diff_values'])
            self.metriques_calculees['cross_entropy_pattern_diff'] = cross_entropy

        # 3. DIVERGENCE JENSEN-SHANNON
        if sequences['ratios_l4'] and sequences['ratios_l5']:
            js_div = self._calculer_divergence_js(sequences['ratios_l4'], sequences['ratios_l5'])
            self.metriques_calculees['js_divergence_l4_l5'] = js_div

    def _calculer_entropies_conditionnelles(self, sequences: Dict[str, List]):
        """Calcule les entropies conditionnelles et jointes."""

        # 1. ENTROPIE CONDITIONNELLE H(Pattern|DIFF)
        if sequences['patterns'] and sequences['diff_values']:
            # Discrétiser DIFF en tranches
            diff_discrete = self._discretiser_valeurs(sequences['diff_values'])
            if diff_discrete:
                h_conditional = self._calculer_entropie_conditionnelle(sequences['patterns'], diff_discrete)
                self.metriques_calculees['conditional_entropy_pattern_diff'] = h_conditional

        # 2. ENTROPIE JOINTE H(L4, L5)
        if sequences['ratios_l4'] and sequences['ratios_l5']:
            # Créer distribution jointe
            joint_entropy = self._calculer_entropie_jointe(sequences['ratios_l4'], sequences['ratios_l5'])
            self.metriques_calculees['joint_entropy_l4_l5'] = joint_entropy

        # 3. INFORMATION MUTUELLE I(Pattern; DIFF)
        if sequences['patterns'] and sequences['diff_values']:
            mutual_info = self._calculer_information_mutuelle_discrete(
                sequences['patterns'], self._discretiser_valeurs(sequences['diff_values'])
            )
            self.metriques_calculees['mutual_info_pattern_diff'] = mutual_info

    def _calculer_metriques_baccarat(self, sequences: Dict[str, List]):
        """Calcule les métriques spécialisées baccarat."""

        # 1. FORMULE LOGARITHMIQUE RÉVOLUTIONNAIRE
        if sequences['diff_values']:
            predictions_log = []
            for diff in sequences['diff_values']:
                # P(S) = 0.45 + 0.35 * log(DIFF + 0.01)
                prob_s = 0.45 + 0.35 * math.log(diff + 0.01)
                predictions_log.append(max(0.0, min(1.0, prob_s)))  # Borner entre 0 et 1

            self.metriques_calculees['predictions_logarithmiques'] = predictions_log
            self.metriques_calculees['moyenne_pred_log'] = np.mean(predictions_log)

        # 2. QUALITÉ DU SIGNAL PRÉDICTIF
        if sequences['diff_values']:
            # Classification par tranches de qualité DIFF
            tranches_qualite = {
                'parfait': [d for d in sequences['diff_values'] if d < 0.020],
                'excellent': [d for d in sequences['diff_values'] if 0.020 <= d < 0.030],
                'tres_bon': [d for d in sequences['diff_values'] if 0.030 <= d < 0.050],
                'douteux': [d for d in sequences['diff_values'] if d > 0.150]
            }

            total = len(sequences['diff_values'])
            for qualite, valeurs in tranches_qualite.items():
                self.metriques_calculees[f'proportion_{qualite}'] = len(valeurs) / total if total > 0 else 0

        # 3. RATIOS ET DIFFÉRENCES AVANCÉS
        if sequences['ratios_l4'] and sequences['ratios_l5']:
            # Métriques dérivées
            sommes = [l4 + l5 for l4, l5 in zip(sequences['ratios_l4'], sequences['ratios_l5'])]
            produits = [l4 * l5 for l4, l5 in zip(sequences['ratios_l4'], sequences['ratios_l5'])]

            self.metriques_calculees['moyenne_somme_ratios'] = np.mean(sommes)
            self.metriques_calculees['moyenne_produit_ratios'] = np.mean(produits)
            self.metriques_calculees['correlation_l4_l5'] = np.corrcoef(sequences['ratios_l4'], sequences['ratios_l5'])[0,1]

    def _calculer_analyses_temporelles(self, sequences: Dict[str, List]):
        """Calcule les analyses temporelles (Markov, ergodique)."""

        # 1. ENTROPIE DE MARKOV pour transitions S→S, S→O, O→S, O→O
        if sequences['patterns'] and len(sequences['patterns']) > 1:
            transitions = []
            for i in range(len(sequences['patterns']) - 1):
                transition = (sequences['patterns'][i], sequences['patterns'][i + 1])
                transitions.append(transition)

            if transitions:
                transition_counts = Counter(transitions)
                total_transitions = len(transitions)
                markov_entropy = 0.0

                for count in transition_counts.values():
                    if count > 0:
                        p = count / total_transitions
                        markov_entropy -= p * math.log2(p)

                self.metriques_calculees['markov_entropy'] = markov_entropy

        # 2. ESTIMATION ERGODIQUE
        if sequences['patterns'] and len(sequences['patterns']) > 100:
            # Estimation de l'entropie ergodique par blocs
            bloc_size = min(10, len(sequences['patterns']) // 10)
            entropies_blocs = []

            for i in range(0, len(sequences['patterns']) - bloc_size + 1, bloc_size):
                bloc = sequences['patterns'][i:i + bloc_size]
                if bloc:
                    bloc_counts = Counter(bloc)
                    bloc_total = len(bloc)
                    bloc_entropy = 0.0

                    for count in bloc_counts.values():
                        if count > 0:
                            p = count / bloc_total
                            bloc_entropy -= p * math.log2(p)

                    entropies_blocs.append(bloc_entropy)

            if entropies_blocs:
                self.metriques_calculees['ergodic_entropy_estimate'] = np.mean(entropies_blocs)

    def _calculer_information_mutuelle(self, sequences: Dict[str, List]):
        """Calcule l'information mutuelle entre différentes métriques."""

        # Information mutuelle entre patterns et DIFF
        if sequences['patterns'] and sequences['diff_values']:
            diff_discrete = self._discretiser_valeurs(sequences['diff_values'])
            if diff_discrete:
                mutual_info = self._calculer_information_mutuelle_discrete(sequences['patterns'], diff_discrete)
                self.metriques_calculees['mutual_info_pattern_diff'] = mutual_info

        # Information mutuelle entre ratios L4 et L5
        if sequences['ratios_l4'] and sequences['ratios_l5']:
            l4_discrete = self._discretiser_valeurs(sequences['ratios_l4'])
            l5_discrete = self._discretiser_valeurs(sequences['ratios_l5'])

            if l4_discrete and l5_discrete:
                mutual_info = self._calculer_information_mutuelle_discrete(l4_discrete, l5_discrete)
                self.metriques_calculees['mutual_info_l4_l5'] = mutual_info

    def _discretiser_valeurs(self, valeurs: List[float], nb_bins: int = 10) -> List[int]:
        """
        Discrétise des valeurs continues en bins.

        Args:
            valeurs (List[float]): Valeurs à discrétiser
            nb_bins (int): Nombre de bins

        Returns:
            List[int]: Valeurs discrétisées
        """
        if not valeurs:
            return []

        valeurs_array = np.array(valeurs)
        min_val, max_val = np.min(valeurs_array), np.max(valeurs_array)

        if min_val == max_val:
            return [0] * len(valeurs)

        bins = np.linspace(min_val, max_val, nb_bins + 1)
        indices = np.digitize(valeurs_array, bins) - 1
        indices = np.clip(indices, 0, nb_bins - 1)

        return indices.tolist()

    def _calculer_divergence_kl(self, p_dist: List[int], q_dist: List[int]) -> float:
        """Calcule la divergence KL entre deux distributions discrètes."""
        if not p_dist or not q_dist:
            return 0.0

        p_counts = Counter(p_dist)
        q_counts = Counter(q_dist)

        # Normaliser
        p_total = len(p_dist)
        q_total = len(q_dist)

        kl_div = 0.0
        for val in set(p_dist + q_dist):
            p_prob = p_counts.get(val, 0) / p_total
            q_prob = q_counts.get(val, 1e-10) / q_total  # Éviter division par 0

            if p_prob > 0:
                kl_div += p_prob * math.log2(p_prob / q_prob)

        return kl_div

    def _calculer_cross_entropy(self, true_labels: List[int], predictions: List[float]) -> float:
        """Calcule la cross-entropy entre labels vrais et prédictions."""
        if not true_labels or not predictions or len(true_labels) != len(predictions):
            return 0.0

        cross_entropy = 0.0
        n = len(true_labels)

        for i in range(n):
            y_true = true_labels[i]
            y_pred = max(1e-10, min(1 - 1e-10, predictions[i]))  # Éviter log(0)

            if y_true == 1:
                cross_entropy -= math.log2(y_pred)
            else:
                cross_entropy -= math.log2(1 - y_pred)

        return cross_entropy / n

    def _calculer_divergence_js(self, p_vals: List[float], q_vals: List[float]) -> float:
        """Calcule la divergence Jensen-Shannon."""
        if not p_vals or not q_vals:
            return 0.0

        # Discrétiser les valeurs
        p_disc = self._discretiser_valeurs(p_vals)
        q_disc = self._discretiser_valeurs(q_vals)

        # Calculer les distributions
        p_counts = Counter(p_disc)
        q_counts = Counter(q_disc)

        all_vals = set(p_disc + q_disc)
        p_total = len(p_disc)
        q_total = len(q_disc)

        # Distribution moyenne M = (P + Q) / 2
        js_div = 0.0

        for val in all_vals:
            p_prob = p_counts.get(val, 0) / p_total
            q_prob = q_counts.get(val, 0) / q_total
            m_prob = (p_prob + q_prob) / 2

            if p_prob > 0 and m_prob > 0:
                js_div += 0.5 * p_prob * math.log2(p_prob / m_prob)
            if q_prob > 0 and m_prob > 0:
                js_div += 0.5 * q_prob * math.log2(q_prob / m_prob)

        return js_div

    def _calculer_entropie_conditionnelle(self, x_vals: List[int], y_vals: List[int]) -> float:
        """Calcule l'entropie conditionnelle H(X|Y)."""
        if not x_vals or not y_vals or len(x_vals) != len(y_vals):
            return 0.0

        # Compter les occurrences jointes
        joint_counts = Counter(zip(x_vals, y_vals))
        y_counts = Counter(y_vals)

        total = len(x_vals)
        h_conditional = 0.0

        for (x, y), joint_count in joint_counts.items():
            p_xy = joint_count / total
            p_y = y_counts[y] / total

            if p_xy > 0 and p_y > 0:
                h_conditional -= p_xy * math.log2(p_xy / p_y)

        return h_conditional

    def _calculer_entropie_jointe(self, x_vals: List[float], y_vals: List[float]) -> float:
        """Calcule l'entropie jointe H(X,Y)."""
        if not x_vals or not y_vals or len(x_vals) != len(y_vals):
            return 0.0

        # Discrétiser les valeurs
        x_disc = self._discretiser_valeurs(x_vals)
        y_disc = self._discretiser_valeurs(y_vals)

        # Compter les occurrences jointes
        joint_counts = Counter(zip(x_disc, y_disc))
        total = len(x_disc)

        joint_entropy = 0.0
        for count in joint_counts.values():
            if count > 0:
                p = count / total
                joint_entropy -= p * math.log2(p)

        return joint_entropy

    def _calculer_information_mutuelle_discrete(self, x_vals: List[int], y_vals: List[int]) -> float:
        """Calcule l'information mutuelle I(X;Y) = H(X) + H(Y) - H(X,Y)."""
        if not x_vals or not y_vals or len(x_vals) != len(y_vals):
            return 0.0

        # Entropies marginales
        x_counts = Counter(x_vals)
        y_counts = Counter(y_vals)
        total = len(x_vals)

        h_x = 0.0
        for count in x_counts.values():
            if count > 0:
                p = count / total
                h_x -= p * math.log2(p)

        h_y = 0.0
        for count in y_counts.values():
            if count > 0:
                p = count / total
                h_y -= p * math.log2(p)

        # Entropie jointe
        joint_counts = Counter(zip(x_vals, y_vals))
        h_xy = 0.0
        for count in joint_counts.values():
            if count > 0:
                p = count / total
                h_xy -= p * math.log2(p)

        return h_x + h_y - h_xy

    def _calculer_metriques_derivees(self, donnees_analyse: List[Dict]):
        """Calcule les métriques dérivées et combinaisons."""

        # Extraire les valeurs
        ratios_l4 = [d.get('ratio_l4', 0.0) for d in donnees_analyse]
        ratios_l5 = [d.get('ratio_l5', 0.0) for d in donnees_analyse]
        diff_values = [d.get('diff', 0.0) for d in donnees_analyse]

        if ratios_l4 and ratios_l5:
            # Métriques dérivées
            sommes_ratios = [l4 + l5 for l4, l5 in zip(ratios_l4, ratios_l5)]
            diff_ratios = [abs(l4 - l5) for l4, l5 in zip(ratios_l4, ratios_l5)]
            produits_ratios = [l4 * l5 for l4, l5 in zip(ratios_l4, ratios_l5)]
            moyennes_ratios = [(l4 + l5) / 2 for l4, l5 in zip(ratios_l4, ratios_l5)]

            # Stocker les métriques dérivées
            self.metriques_calculees['sommes_ratios'] = sommes_ratios
            self.metriques_calculees['diff_ratios'] = diff_ratios
            self.metriques_calculees['produits_ratios'] = produits_ratios
            self.metriques_calculees['moyennes_ratios'] = moyennes_ratios

            # Statistiques des métriques dérivées
            self.metriques_calculees['moyenne_sommes_ratios'] = np.mean(sommes_ratios)
            self.metriques_calculees['std_sommes_ratios'] = np.std(sommes_ratios)
            self.metriques_calculees['moyenne_diff_ratios'] = np.mean(diff_ratios)
            self.metriques_calculees['std_diff_ratios'] = np.std(diff_ratios)

    def _calculer_ecarts_types(self, donnees_analyse: List[Dict]):
        """Calcule les écarts-types de toutes les métriques."""

        # Métriques de base
        metriques_base = ['ratio_l4', 'ratio_l5', 'diff', 'entropie_l4', 'entropie_l5', 'entropie_globale']

        for metrique in metriques_base:
            valeurs = [d.get(metrique, 0.0) for d in donnees_analyse if metrique in d]
            if valeurs:
                self.metriques_calculees[f'std_{metrique}'] = np.std(valeurs)
                self.metriques_calculees[f'var_{metrique}'] = np.var(valeurs)
                self.metriques_calculees[f'min_{metrique}'] = np.min(valeurs)
                self.metriques_calculees[f'max_{metrique}'] = np.max(valeurs)
                self.metriques_calculees[f'median_{metrique}'] = np.median(valeurs)


# ============================================================================
# CLASSE : ANALYSEUR SIGNIFICANCE MÉTRIQUES
# ============================================================================

class AnalyseurSignificanceMetriques:
    """
    ANALYSEUR DE SIGNIFICANCE DES MÉTRIQUES POUR S/O
    ===============================================

    Identifie quelles métriques à la main N sont significatives
    pour expliquer les transitions S/O à la main N+1.

    ANALYSES RÉALISÉES :
    1. Corrélations métriques vs patterns S/O
    2. Tests de significance statistique
    3. Classification par force prédictive
    4. Identification des métriques critiques
    """

    def __init__(self):
        """Initialise l'analyseur de significance."""
        self.resultats_significance = {}
        self.metriques_significatives = {}

    def analyser_significance_so(self, donnees_analyse: List[Dict],
                               metriques_globales: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyse la significance des métriques pour prédire S/O.

        Args:
            donnees_analyse (List[Dict]): Données d'analyse par main
            metriques_globales (Dict[str, Any]): Métriques globales calculées

        Returns:
            Dict[str, Any]: Résultats de l'analyse de significance
        """
        print("   🎯 Analyse significance métriques pour S/O...")

        # Séparer les données S et O
        donnees_s = [d for d in donnees_analyse if d.get('pattern') == 'S']
        donnees_o = [d for d in donnees_analyse if d.get('pattern') == 'O']

        print(f"   📊 Données S: {len(donnees_s):,}, Données O: {len(donnees_o):,}")

        # Analyser chaque métrique
        self._analyser_metriques_individuelles(donnees_s, donnees_o)

        # Analyser les combinaisons de métriques
        self._analyser_combinaisons_metriques(donnees_s, donnees_o)

        # Classer par significance
        self._classer_par_significance()

        print(f"   ✅ {len(self.metriques_significatives)} métriques significatives identifiées")

        return {
            'metriques_significatives': self.metriques_significatives,
            'resultats_tests': self.resultats_significance,
            'statistiques': {
                'nb_donnees_s': len(donnees_s),
                'nb_donnees_o': len(donnees_o),
                'nb_metriques_testees': len(self.resultats_significance)
            }
        }

    def _analyser_metriques_individuelles(self, donnees_s: List[Dict], donnees_o: List[Dict]):
        """
        PHASE 3 : Analyse exhaustive de la significance de TOUTES les métriques individuellement.
        Analyse les ~130-140 métriques selon les spécifications.
        """

        # Identifier TOUTES les métriques disponibles dans les données
        toutes_metriques = set()
        for donnee in donnees_s + donnees_o:
            toutes_metriques.update(donnee.keys())

        # Exclure les champs non-métriques
        metriques_a_tester = toutes_metriques - {'partie_id', 'main', 'pattern', 'index3', 'index5'}

        print(f"      📊 Analyse de {len(metriques_a_tester)} métriques individuelles")

        for metrique in metriques_a_tester:
            try:
                # Extraire les valeurs pour S et O (seulement les valeurs numériques)
                valeurs_s = [d.get(metrique, 0.0) for d in donnees_s
                           if metrique in d and isinstance(d.get(metrique), (int, float)) and not np.isnan(d.get(metrique, 0.0))]
                valeurs_o = [d.get(metrique, 0.0) for d in donnees_o
                           if metrique in d and isinstance(d.get(metrique), (int, float)) and not np.isnan(d.get(metrique, 0.0))]

                if len(valeurs_s) >= 30 and len(valeurs_o) >= 30:  # Minimum statistique robuste
                    # Test t de Student
                    t_stat, p_value = self._test_t_student(valeurs_s, valeurs_o)

                    # Statistiques descriptives
                    moyenne_s = np.mean(valeurs_s)
                    moyenne_o = np.mean(valeurs_o)
                    diff_moyennes = abs(moyenne_s - moyenne_o)
                    std_s = np.std(valeurs_s)
                    std_o = np.std(valeurs_o)

                    # Coefficient de séparation
                    coeff_separation = self._calculer_coefficient_separation(valeurs_s, valeurs_o)

                    # Calcul de la corrélation avec les patterns S/O
                    correlation = self._calculer_correlation_pattern(valeurs_s, valeurs_o)

                    # R² (variance expliquée)
                    r_squared = correlation ** 2

                    # Stabilité temporelle
                    stabilite = self._calculer_stabilite_temporelle_metrique(donnees_s + donnees_o, metrique)

                    # Correction de Bonferroni pour tests multiples
                    p_value_corrigee = min(p_value * len(metriques_a_tester), 1.0) if p_value is not None else 1.0

                    # Seuils optimaux pour prédiction
                    seuil_optimal_s, precision_s = self._calculer_seuil_optimal(valeurs_s, valeurs_o, 'S')
                    seuil_optimal_o, precision_o = self._calculer_seuil_optimal(valeurs_s, valeurs_o, 'O')

                    self.resultats_significance[metrique] = {
                        't_statistic': t_stat,
                        'p_value': p_value,
                        'p_value_corrigee': p_value_corrigee,
                        'moyenne_s': moyenne_s,
                        'moyenne_o': moyenne_o,
                        'std_s': std_s,
                        'std_o': std_o,
                        'difference_moyennes': diff_moyennes,
                        'coefficient_separation': coeff_separation,
                        'correlation': correlation,
                        'r_squared': r_squared,
                        'stabilite': stabilite,
                        'seuil_optimal_s': seuil_optimal_s,
                        'precision_s': precision_s,
                        'seuil_optimal_o': seuil_optimal_o,
                        'precision_o': precision_o,
                        'nb_valeurs_s': len(valeurs_s),
                        'nb_valeurs_o': len(valeurs_o),
                        'significatif': p_value_corrigee < 0.05 if p_value_corrigee is not None else False
                    }

            except Exception as e:
                print(f"      ⚠️ Erreur analyse métrique {metrique}: {e}")
                continue

    def _calculer_correlation_pattern(self, valeurs_s: List[float], valeurs_o: List[float]) -> float:
        """Calcule la corrélation entre une métrique et les patterns S/O."""
        try:
            # Créer les vecteurs : 1 pour S, 0 pour O
            patterns = [1] * len(valeurs_s) + [0] * len(valeurs_o)
            valeurs = valeurs_s + valeurs_o

            if len(patterns) != len(valeurs) or len(valeurs) < 2:
                return 0.0

            correlation_matrix = np.corrcoef(patterns, valeurs)
            correlation = correlation_matrix[0, 1]

            return correlation if not np.isnan(correlation) else 0.0
        except:
            return 0.0

    def _calculer_stabilite_temporelle_metrique(self, donnees: List[Dict], nom_metrique: str) -> float:
        """Calcule la stabilité temporelle d'une métrique."""
        try:
            valeurs = [d.get(nom_metrique, 0.0) for d in donnees
                      if nom_metrique in d and isinstance(d.get(nom_metrique), (int, float))]

            if len(valeurs) < 20:
                return 0.0

            # Diviser en 4 tranches temporelles
            taille_tranche = len(valeurs) // 4
            moyennes_tranches = []

            for i in range(4):
                debut = i * taille_tranche
                fin = (i + 1) * taille_tranche if i < 3 else len(valeurs)
                tranche = valeurs[debut:fin]
                if tranche:
                    moyennes_tranches.append(np.mean(tranche))

            if len(moyennes_tranches) < 2:
                return 0.0

            # Stabilité = 1 - coefficient de variation des moyennes
            cv = np.std(moyennes_tranches) / np.mean(moyennes_tranches) if np.mean(moyennes_tranches) != 0 else 1.0
            stabilite = max(0.0, 1.0 - cv)

            return stabilite
        except:
            return 0.0

    def _calculer_seuil_optimal(self, valeurs_s: List[float], valeurs_o: List[float], pattern_cible: str) -> tuple:
        """Calcule le seuil optimal pour prédire un pattern donné."""
        try:
            if pattern_cible == 'S':
                # Pour S, on cherche le seuil qui maximise la précision pour prédire S
                valeurs_positives = valeurs_s
                valeurs_negatives = valeurs_o
            else:
                # Pour O, on cherche le seuil qui maximise la précision pour prédire O
                valeurs_positives = valeurs_o
                valeurs_negatives = valeurs_s

            # Tester différents seuils
            tous_valeurs = sorted(valeurs_positives + valeurs_negatives)
            meilleur_seuil = 0.0
            meilleure_precision = 0.0

            for i in range(1, len(tous_valeurs), max(1, len(tous_valeurs) // 20)):  # Tester 20 seuils
                seuil = tous_valeurs[i]

                if pattern_cible == 'S':
                    # Prédire S si valeur >= seuil
                    tp = len([v for v in valeurs_positives if v >= seuil])  # Vrais positifs
                    fp = len([v for v in valeurs_negatives if v >= seuil])  # Faux positifs
                else:
                    # Prédire O si valeur < seuil
                    tp = len([v for v in valeurs_positives if v < seuil])  # Vrais positifs
                    fp = len([v for v in valeurs_negatives if v < seuil])  # Faux positifs

                precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0

                if precision > meilleure_precision:
                    meilleure_precision = precision
                    meilleur_seuil = seuil

            return meilleur_seuil, meilleure_precision
        except:
            return 0.0, 0.0

    def _analyser_combinaisons_metriques(self, donnees_s: List[Dict], donnees_o: List[Dict]):
        """Analyse les combinaisons de métriques."""

        # Combinaisons importantes
        combinaisons = [
            ('ratio_l4', 'ratio_l5'),
            ('diff', 'ratio_l4'),
            ('diff', 'ratio_l5'),
            ('entropie_l4', 'entropie_l5')
        ]

        for metrique1, metrique2 in combinaisons:
            # Créer des scores combinés
            scores_s = []
            scores_o = []

            for d in donnees_s:
                if metrique1 in d and metrique2 in d:
                    score = d[metrique1] * d[metrique2]  # Produit simple
                    scores_s.append(score)

            for d in donnees_o:
                if metrique1 in d and metrique2 in d:
                    score = d[metrique1] * d[metrique2]
                    scores_o.append(score)

            if scores_s and scores_o:
                # Test de significance
                t_stat, p_value = self._test_t_student(scores_s, scores_o)

                nom_combinaison = f"{metrique1}_x_{metrique2}"
                self.resultats_significance[nom_combinaison] = {
                    't_statistic': t_stat,
                    'p_value': p_value,
                    'moyenne_s': np.mean(scores_s),
                    'moyenne_o': np.mean(scores_o),
                    'significatif': p_value < 0.05 if p_value is not None else False
                }

    def _classer_par_significance(self):
        """Classe les métriques par ordre de significance."""

        # Trier par p-value (plus petit = plus significatif)
        metriques_triees = sorted(
            self.resultats_significance.items(),
            key=lambda x: x[1].get('p_value', 1.0)
        )

        # Classer par force
        for nom_metrique, resultats in metriques_triees:
            p_value = resultats.get('p_value', 1.0)
            diff_moyennes = resultats.get('difference_moyennes', 0.0)

            if p_value < 0.001 and diff_moyennes > 0.1:
                force = 'FORTE'
            elif p_value < 0.01 and diff_moyennes > 0.05:
                force = 'MODÉRÉE'
            elif p_value < 0.05:
                force = 'FAIBLE'
            else:
                force = 'NON_SIGNIFICATIVE'

            if force != 'NON_SIGNIFICATIVE':
                self.metriques_significatives[nom_metrique] = {
                    'force': force,
                    'p_value': p_value,
                    'difference_moyennes': diff_moyennes,
                    'rang': len(self.metriques_significatives) + 1
                }

    def _test_t_student(self, echantillon1: List[float], echantillon2: List[float]) -> tuple:
        """
        Effectue un test t de Student entre deux échantillons.

        Returns:
            tuple: (t_statistic, p_value)
        """
        if not echantillon1 or not echantillon2:
            return None, None

        n1, n2 = len(echantillon1), len(echantillon2)
        if n1 < 2 or n2 < 2:
            return None, None

        # Moyennes et variances
        m1, m2 = np.mean(echantillon1), np.mean(echantillon2)
        v1, v2 = np.var(echantillon1, ddof=1), np.var(echantillon2, ddof=1)

        # Test t avec variances inégales (Welch)
        if v1 == 0 and v2 == 0:
            return None, None

        s_pooled = math.sqrt(((n1 - 1) * v1 + (n2 - 1) * v2) / (n1 + n2 - 2))
        if s_pooled == 0:
            return None, None

        t_stat = (m1 - m2) / (s_pooled * math.sqrt(1/n1 + 1/n2))

        # Approximation simple de la p-value (pour éviter scipy)
        df = n1 + n2 - 2
        p_value = 2 * (1 - self._cdf_t_approx(abs(t_stat), df))

        return t_stat, p_value

    def _cdf_t_approx(self, t: float, df: int) -> float:
        """Approximation simple de la CDF de la distribution t."""
        # Approximation très basique pour éviter les dépendances
        if df > 30:
            # Approximation normale pour df élevé
            return 0.5 * (1 + math.erf(t / math.sqrt(2)))
        else:
            # Approximation grossière
            return min(0.999, max(0.001, 0.5 + t / (2 * math.sqrt(df))))

    def _calculer_coefficient_separation(self, valeurs1: List[float], valeurs2: List[float]) -> float:
        """Calcule un coefficient de séparation entre deux distributions."""
        if not valeurs1 or not valeurs2:
            return 0.0

        m1, m2 = np.mean(valeurs1), np.mean(valeurs2)
        s1, s2 = np.std(valeurs1), np.std(valeurs2)

        if s1 + s2 == 0:
            return 0.0

        return abs(m1 - m2) / (s1 + s2)


# ============================================================================
# CLASSE : GÉNÉRATEUR RAPPORT ANALYSE
# ============================================================================

class GenerateurRapportAnalyse:
    """
    GÉNÉRATEUR DE RAPPORT D'ANALYSE COMPLET
    ======================================

    Génère un rapport détaillé avec :
    1. Statistiques générales de l'analyse
    2. Métriques significatives identifiées
    3. Recommandations prédictives
    4. Tableaux de résultats
    """

    def __init__(self):
        """Initialise le générateur de rapport."""
        pass

    def generer_rapport_complet(self, donnees_analyse: List[Dict],
                              metriques_globales: Dict[str, Any],
                              resultats_significance: Dict[str, Any]) -> str:
        """
        Génère le rapport complet d'analyse.

        Args:
            donnees_analyse (List[Dict]): Données d'analyse
            metriques_globales (Dict[str, Any]): Métriques globales
            resultats_significance (Dict[str, Any]): Résultats significance

        Returns:
            str: Rapport complet formaté
        """
        print("   📋 Génération du rapport avancé complet...")

        rapport = []
        rapport.append("=" * 100)
        rapport.append("RAPPORT D'ANALYSE BACCARAT AVANCÉ - ANABCT.PY")
        rapport.append("=" * 100)
        rapport.append(f"Date génération : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        rapport.append("")

        # SECTION 1: RÉSUMÉ EXÉCUTIF
        rapport.extend(self._generer_resume_executif(donnees_analyse, resultats_significance))

        # SECTION 2: STATISTIQUES GÉNÉRALES DÉTAILLÉES
        rapport.extend(self._generer_section_statistiques(donnees_analyse))

        # SECTION 3: ANALYSE COMPLÈTE DES MÉTRIQUES SIGNIFICATIVES
        rapport.extend(self._generer_section_metriques_significatives_avancee(resultats_significance))

        # SECTION 4: ANALYSE SPÉCIALE VARIABLE DIFF
        rapport.extend(self._generer_section_analyse_diff(donnees_analyse))

        # SECTION 5: RECOMMANDATIONS STRATÉGIQUES
        rapport.extend(self._generer_section_recommandations_avancees(resultats_significance))

        # SECTION 6: PERFORMANCE ET PRÉDICTIBILITÉ
        rapport.extend(self._generer_section_performance(donnees_analyse, resultats_significance))

        rapport_final = "\n".join(rapport)

        # Sauvegarder le rapport
        nom_fichier = f"rapport_anabct_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(nom_fichier, 'w', encoding='utf-8') as f:
            f.write(rapport_final)

        print(f"   ✅ Rapport sauvegardé : {nom_fichier}")

        return rapport_final

    def _generer_resume_executif(self, donnees_analyse: List[Dict], resultats_significance: Dict[str, Any]) -> List[str]:
        """Génère le résumé exécutif avec les métriques les plus significatives."""
        section = []
        section.append("1. RÉSUMÉ EXÉCUTIF")
        section.append("-" * 50)

        # Compter les patterns
        patterns_s = len([d for d in donnees_analyse if d.get('pattern') == 'S'])
        patterns_o = len([d for d in donnees_analyse if d.get('pattern') == 'O'])
        patterns_e = len([d for d in donnees_analyse if d.get('pattern') == 'E'])
        total_patterns = patterns_s + patterns_o + patterns_e

        section.append(f"📊 DONNÉES ANALYSÉES :")
        section.append(f"  • Total mains analysées : {len(donnees_analyse):,}")
        section.append(f"  • Patterns S (continuation) : {patterns_s:,} ({patterns_s/total_patterns*100:.1f}%)")
        section.append(f"  • Patterns O (alternance) : {patterns_o:,} ({patterns_o/total_patterns*100:.1f}%)")
        section.append(f"  • Patterns E (égalité) : {patterns_e:,} ({patterns_e/total_patterns*100:.1f}%)")
        section.append("")

        # Métriques significatives identifiées
        metriques_sig = resultats_significance.get('metriques_significatives', {})
        section.append(f"🎯 MÉTRIQUES SIGNIFICATIVES IDENTIFIÉES : {len(metriques_sig)}")

        # Top 5 métriques les plus significatives
        if metriques_sig:
            metriques_triees = sorted(metriques_sig.items(),
                                    key=lambda x: x[1].get('p_value', 1.0))[:5]
            section.append("  TOP 5 MÉTRIQUES PRIORITAIRES :")
            for i, (nom, info) in enumerate(metriques_triees, 1):
                p_val = info.get('p_value', 1.0)
                diff_moy = info.get('difference_moyennes', 0.0)
                section.append(f"    {i}. {nom} (p-value={p_val:.6f}, diff={diff_moy:.6f})")

        section.append("")
        return section

    def _generer_section_metriques_significatives_avancee(self, resultats_significance: Dict[str, Any]) -> List[str]:
        """Génère l'analyse complète des métriques significatives."""
        section = []
        section.append("3. ANALYSE COMPLÈTE DES MÉTRIQUES SIGNIFICATIVES")
        section.append("-" * 60)

        metriques_sig = resultats_significance.get('metriques_significatives', {})

        if not metriques_sig:
            section.append("Aucune métrique significative identifiée.")
            section.append("")
            return section

        # Classer par niveau de significance
        forte_sig = {k: v for k, v in metriques_sig.items() if v.get('force') == 'FORTE'}
        moderee_sig = {k: v for k, v in metriques_sig.items() if v.get('force') == 'MODÉRÉE'}
        faible_sig = {k: v for k, v in metriques_sig.items() if v.get('force') == 'FAIBLE'}

        section.append(f"📈 RÉPARTITION PAR NIVEAU DE SIGNIFICANCE :")
        section.append(f"  • FORTE SIGNIFICANCE : {len(forte_sig)} métriques")
        section.append(f"  • MODÉRÉE SIGNIFICANCE : {len(moderee_sig)} métriques")
        section.append(f"  • FAIBLE SIGNIFICANCE : {len(faible_sig)} métriques")
        section.append("")

        # Détail des métriques à forte significance
        if forte_sig:
            section.append("🔥 MÉTRIQUES À FORTE SIGNIFICANCE :")
            for nom, info in forte_sig.items():
                p_val = info.get('p_value', 1.0)
                diff_moy = info.get('difference_moyennes', 0.0)
                section.append(f"  • {nom} : p-value={p_val:.6f}, diff_moyennes={diff_moy:.6f}")

        # Détail des métriques à significance modérée
        if moderee_sig:
            section.append("")
            section.append("📊 MÉTRIQUES À MODÉRÉE SIGNIFICANCE :")
            for nom, info in moderee_sig.items():
                p_val = info.get('p_value', 1.0)
                diff_moy = info.get('difference_moyennes', 0.0)
                section.append(f"  • {nom} : p-value={p_val:.6f}, diff_moyennes={diff_moy:.6f}")

        # Détail des métriques à faible significance
        if faible_sig:
            section.append("")
            section.append("📉 MÉTRIQUES À FAIBLE SIGNIFICANCE :")
            for nom, info in faible_sig.items():
                p_val = info.get('p_value', 1.0)
                diff_moy = info.get('difference_moyennes', 0.0)
                section.append(f"  • {nom} : p-value={p_val:.6f}, diff_moyennes={diff_moy:.6f}")

        section.append("")
        return section

    def _generer_section_recommandations_avancees(self, resultats_significance: Dict[str, Any]) -> List[str]:
        """Génère les recommandations stratégiques avancées."""
        section = []
        section.append("5. RECOMMANDATIONS STRATÉGIQUES")
        section.append("-" * 50)

        metriques_sig = resultats_significance.get('metriques_significatives', {})

        if not metriques_sig:
            section.append("❌ Aucune recommandation disponible - pas de métriques significatives.")
            section.append("")
            return section

        # Métriques prioritaires (forte significance)
        prioritaires = [k for k, v in metriques_sig.items() if v.get('force') == 'FORTE']
        secondaires = [k for k, v in metriques_sig.items() if v.get('force') == 'MODÉRÉE']

        section.append("🎯 MÉTRIQUES PRIORITAIRES POUR PRÉDICTION S/O :")
        if prioritaires:
            for i, metrique in enumerate(prioritaires[:5], 1):
                info = metriques_sig[metrique]
                precision_s = info.get('precision_s', 0.0)
                precision_o = info.get('precision_o', 0.0)
                section.append(f"  {i}. {metrique}")
                section.append(f"     • Précision S : {precision_s:.1%}")
                section.append(f"     • Précision O : {precision_o:.1%}")
                section.append(f"     • Seuil optimal S : {info.get('seuil_optimal_s', 0.0):.6f}")
                section.append(f"     • Seuil optimal O : {info.get('seuil_optimal_o', 0.0):.6f}")
        else:
            section.append("  Aucune métrique à forte significance identifiée.")

        section.append("")
        section.append("📊 MÉTRIQUES SECONDAIRES (SUPPORT) :")
        if secondaires:
            for metrique in secondaires[:5]:
                section.append(f"  • {metrique}")
        else:
            section.append("  Aucune métrique à significance modérée identifiée.")

        section.append("")
        section.append("⚡ STRATÉGIE DE PRÉDICTION RECOMMANDÉE :")
        section.append("  1. Utiliser les métriques prioritaires comme indicateurs principaux")
        section.append("  2. Combiner avec les métriques secondaires pour confirmation")
        section.append("  3. Appliquer les seuils optimaux identifiés")
        section.append("  4. Surveiller la stabilité temporelle des métriques")

        section.append("")
        return section

    def _generer_section_performance(self, donnees_analyse: List[Dict], resultats_significance: Dict[str, Any]) -> List[str]:
        """Génère la section performance et prédictibilité."""
        section = []
        section.append("6. PERFORMANCE ET PRÉDICTIBILITÉ")
        section.append("-" * 50)

        # Calculer les statistiques de performance
        patterns_s = [d for d in donnees_analyse if d.get('pattern') == 'S']
        patterns_o = [d for d in donnees_analyse if d.get('pattern') == 'O']

        section.append("📈 DISTRIBUTION DES PATTERNS :")
        total = len(patterns_s) + len(patterns_o)
        if total > 0:
            section.append(f"  • Pourcentage S : {len(patterns_s)/total*100:.1f}%")
            section.append(f"  • Pourcentage O : {len(patterns_o)/total*100:.1f}%")
            section.append(f"  • Équilibre S/O : {'✅ Équilibré' if abs(len(patterns_s) - len(patterns_o))/total < 0.1 else '⚠️ Déséquilibré'}")

        section.append("")

        # Performance des métriques
        metriques_sig = resultats_significance.get('metriques_significatives', {})
        if metriques_sig:
            precisions_s = [v.get('precision_s', 0.0) for v in metriques_sig.values() if v.get('precision_s')]
            precisions_o = [v.get('precision_o', 0.0) for v in metriques_sig.values() if v.get('precision_o')]

            if precisions_s and precisions_o:
                section.append("🎯 PERFORMANCE PRÉDICTIVE :")
                section.append(f"  • Précision moyenne S : {np.mean(precisions_s):.1%}")
                section.append(f"  • Précision moyenne O : {np.mean(precisions_o):.1%}")
                section.append(f"  • Précision globale : {(np.mean(precisions_s) + np.mean(precisions_o))/2:.1%}")

                # Évaluation de la qualité
                precision_globale = (np.mean(precisions_s) + np.mean(precisions_o))/2
                if precision_globale > 0.7:
                    qualite = "🟢 EXCELLENTE"
                elif precision_globale > 0.6:
                    qualite = "🟡 BONNE"
                elif precision_globale > 0.5:
                    qualite = "🟠 MOYENNE"
                else:
                    qualite = "🔴 FAIBLE"

                section.append(f"  • Qualité prédictive : {qualite}")

        section.append("")
        return section

    def _generer_section_statistiques(self, donnees_analyse: List[Dict]) -> List[str]:
        """Génère la section des statistiques générales."""
        section = []
        section.append("1. STATISTIQUES GÉNÉRALES")
        section.append("-" * 40)

        # Compter les patterns
        patterns_s = len([d for d in donnees_analyse if d.get('pattern') == 'S'])
        patterns_o = len([d for d in donnees_analyse if d.get('pattern') == 'O'])
        patterns_e = len([d for d in donnees_analyse if d.get('pattern') == 'E'])
        total_patterns = patterns_s + patterns_o

        section.append(f"Total mains analysées : {len(donnees_analyse):,}")

        if total_patterns > 0:
            section.append(f"Patterns S (continuation) : {patterns_s:,} ({patterns_s/total_patterns*100:.1f}%)")
            section.append(f"Patterns O (alternance) : {patterns_o:,} ({patterns_o/total_patterns*100:.1f}%)")
        else:
            section.append(f"Patterns S (continuation) : {patterns_s:,}")
            section.append(f"Patterns O (alternance) : {patterns_o:,}")

        section.append(f"Patterns E (indéterminés) : {patterns_e:,}")
        section.append("")

        # Statistiques DIFF
        diff_values = [d.get('diff', 0.0) for d in donnees_analyse if 'diff' in d]
        if diff_values:
            section.append("ANALYSE VARIABLE DIFF :")
            section.append(f"  Moyenne DIFF : {np.mean(diff_values):.6f}")
            section.append(f"  Médiane DIFF : {np.median(diff_values):.6f}")
            section.append(f"  Écart-type DIFF : {np.std(diff_values):.6f}")
            section.append(f"  Min DIFF : {np.min(diff_values):.6f}")
            section.append(f"  Max DIFF : {np.max(diff_values):.6f}")

            # Tranches de qualité
            parfait = len([d for d in diff_values if d < 0.020])
            excellent = len([d for d in diff_values if 0.020 <= d < 0.030])
            tres_bon = len([d for d in diff_values if 0.030 <= d < 0.050])
            douteux = len([d for d in diff_values if d > 0.150])

            section.append("")
            section.append("TRANCHES DE QUALITÉ DIFF :")
            total_diff = len(diff_values)
            if total_diff > 0:
                section.append(f"  Parfait (< 0.020) : {parfait:,} ({parfait/total_diff*100:.1f}%)")
                section.append(f"  Excellent (0.020-0.030) : {excellent:,} ({excellent/total_diff*100:.1f}%)")
                section.append(f"  Très bon (0.030-0.050) : {tres_bon:,} ({tres_bon/total_diff*100:.1f}%)")
                section.append(f"  Douteux (> 0.150) : {douteux:,} ({douteux/total_diff*100:.1f}%)")
            else:
                section.append(f"  Parfait (< 0.020) : {parfait:,}")
                section.append(f"  Excellent (0.020-0.030) : {excellent:,}")
                section.append(f"  Très bon (0.030-0.050) : {tres_bon:,}")
                section.append(f"  Douteux (> 0.150) : {douteux:,}")

        section.append("")
        return section

    def _generer_section_metriques_significatives(self, resultats_significance: Dict[str, Any]) -> List[str]:
        """Génère la section des métriques significatives."""
        section = []
        section.append("2. MÉTRIQUES SIGNIFICATIVES POUR S/O")
        section.append("-" * 40)

        metriques_sig = resultats_significance.get('metriques_significatives', {})

        if not metriques_sig:
            section.append("Aucune métrique significative identifiée.")
            section.append("")
            return section

        # Trier par force
        metriques_par_force = {'FORTE': [], 'MODÉRÉE': [], 'FAIBLE': []}

        for nom, info in metriques_sig.items():
            force = info.get('force', 'FAIBLE')
            if force in metriques_par_force:
                metriques_par_force[force].append((nom, info))

        for force, metriques in metriques_par_force.items():
            if metriques:
                section.append(f"{force} SIGNIFICANCE :")
                for nom, info in metriques:
                    p_val = info.get('p_value', 1.0)
                    diff_moy = info.get('difference_moyennes', 0.0)
                    section.append(f"  {nom} : p-value={p_val:.6f}, diff_moyennes={diff_moy:.6f}")
                section.append("")

        return section

    def _generer_section_analyse_diff(self, donnees_analyse: List[Dict]) -> List[str]:
        """Génère la section d'analyse spéciale DIFF."""
        section = []
        section.append("3. ANALYSE SPÉCIALE VARIABLE DIFF")
        section.append("-" * 40)

        # Analyser DIFF par pattern
        donnees_s = [d for d in donnees_analyse if d.get('pattern') == 'S']
        donnees_o = [d for d in donnees_analyse if d.get('pattern') == 'O']

        diff_s = [d.get('diff', 0.0) for d in donnees_s if 'diff' in d]
        diff_o = [d.get('diff', 0.0) for d in donnees_o if 'diff' in d]

        if diff_s and diff_o:
            section.append("DIFF POUR PATTERNS S (continuation) :")
            section.append(f"  Moyenne : {np.mean(diff_s):.6f}")
            section.append(f"  Médiane : {np.median(diff_s):.6f}")
            section.append(f"  Écart-type : {np.std(diff_s):.6f}")
            section.append("")

            section.append("DIFF POUR PATTERNS O (alternance) :")
            section.append(f"  Moyenne : {np.mean(diff_o):.6f}")
            section.append(f"  Médiane : {np.median(diff_o):.6f}")
            section.append(f"  Écart-type : {np.std(diff_o):.6f}")
            section.append("")

            # Différence
            diff_moyennes = abs(np.mean(diff_s) - np.mean(diff_o))
            section.append(f"DIFFÉRENCE ABSOLUE DES MOYENNES : {diff_moyennes:.6f}")

            if diff_moyennes > 0.01:
                section.append("→ DIFFÉRENCE SIGNIFICATIVE détectée !")
            else:
                section.append("→ Différence faible entre S et O")

        section.append("")
        return section

    def _generer_section_recommandations(self, resultats_significance: Dict[str, Any]) -> List[str]:
        """Génère la section des recommandations."""
        section = []
        section.append("4. RECOMMANDATIONS PRÉDICTIVES")
        section.append("-" * 40)

        metriques_sig = resultats_significance.get('metriques_significatives', {})

        if not metriques_sig:
            section.append("• Aucune métrique significative → Prédiction difficile")
            section.append("• Recommandation : Analyser plus de données ou ajuster les seuils")
        else:
            section.append("MÉTRIQUES RECOMMANDÉES POUR PRÉDICTION :")

            # Top 3 des métriques les plus significatives
            metriques_triees = sorted(
                metriques_sig.items(),
                key=lambda x: x[1].get('p_value', 1.0)
            )[:3]

            for i, (nom, info) in enumerate(metriques_triees, 1):
                force = info.get('force', 'FAIBLE')
                section.append(f"{i}. {nom} (Force: {force})")

            section.append("")
            section.append("STRATÉGIE RECOMMANDÉE :")
            section.append("• Utiliser les métriques FORTE significance en priorité")
            section.append("• Combiner plusieurs métriques pour améliorer la précision")
            section.append("• Surveiller la variable DIFF comme indicateur de qualité")
            section.append("• Éviter les prédictions quand DIFF > 0.150 (signal douteux)")

        section.append("")
        section.append("=" * 80)
        return section


# ============================================================================
# FONCTION PRINCIPALE ET TESTS
# ============================================================================

def main():
    """Fonction principale pour tester l'analyseur."""

    dataset_path = "dataset_baccarat_lupasco_20250624_104837.json"

    print("LANCEMENT ANALYSEUR BACCARAT OPTIMISE - ANABCT.PY")
    print("=" * 80)

    # Créer l'analyseur
    analyseur = AnalyseurBaccaratOptimise(dataset_path)

    # Exécuter l'analyse complète
    resultats = analyseur.executer_analyse_complete()

    if 'erreur' in resultats:
        print(f"ERREUR : {resultats['erreur']}")
        return False

    # Afficher les statistiques finales
    stats = resultats.get('statistiques', {})
    print(f"\nANALYSE TERMINEE AVEC SUCCES")
    print(f"Parties traitees : {stats.get('parties_traitees', 0):,}")
    print(f"Mains analysees : {stats.get('mains_analysees', 0):,}")
    print(f"Metriques par main : {stats.get('metriques_par_main', 0):,}")

    # DEBUG : Compter les patterns S/O dans les données
    donnees_analyse = resultats.get('donnees_analyse', [])
    patterns_s = [d for d in donnees_analyse if d.get('pattern') == 'S']
    patterns_o = [d for d in donnees_analyse if d.get('pattern') == 'O']
    patterns_e = [d for d in donnees_analyse if d.get('pattern') == 'E']
    patterns_autres = [d for d in donnees_analyse if d.get('pattern') not in ['S', 'O', 'E']]

    print(f"\nDEBUG PATTERNS:")
    print(f"Patterns S: {len(patterns_s)}")
    print(f"Patterns O: {len(patterns_o)}")
    print(f"Patterns E: {len(patterns_e)}")
    print(f"Patterns autres: {len(patterns_autres)}")

    if patterns_autres:
        print(f"Exemples patterns autres: {[d.get('pattern') for d in patterns_autres[:5]]}")

    return True


if __name__ == "__main__":
    main()
