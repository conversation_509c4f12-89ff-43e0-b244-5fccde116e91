#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR BACCARAT OPTIMISÉ - ANABCT.PY
======================================

NOUVEL ANALYSEUR BACCARAT SELON SPÉCIFICATIONS COMPLÈTES
Analyse main par main de 10,000 parties avec ~130-140 métriques
Identification des métriques significatives pour patterns S/O

ARCHITECTURE :
- AnalyseurBaccaratOptimise : Classe principale orchestrant l'analyse
- CalculateurMetriquesEntropiques : Calcul des ~130-140 métriques par main
- AnalyseurSignificanceMetriques : Identification métriques significatives S/O
- GenerateurRapportAnalyse : Génération rapports détaillés

CONTRAINTES RESPECTÉES :
- Pas de nouveaux fichiers Python (exception accordée pour anabct.py)
- Consolidation des dépendances dans classes intégrées
- Optimisation 28GB RAM et 8 CPU cores
- Corrélations désactivées pour performance
- Chargement direct sans analyseur_transitions_index5.py

Auteur: Expert Développeur Senior
Date: 2025-06-25 - Version PROFESSIONNELLE
"""

import json
import os
import math
import numpy as np
from datetime import datetime
from collections import Counter
import gc
from typing import Dict, List, Optional, Any

# ============================================================================
# CLASSE PRINCIPALE : ANALYSEUR BACCARAT OPTIMISÉ
# ============================================================================

class AnalyseurBaccaratOptimise:
    """
    CLASSE PRINCIPALE ORCHESTRANT L'ANALYSE COMPLÈTE
    ===============================================
    
    Analyse main par main de 10,000 parties de baccarat
    Calcul de ~130-140 métriques entropiques par main
    Identification des métriques significatives pour patterns S/O
    
    FLUX D'ANALYSE :
    1. Chargement direct du dataset JSON
    2. Boucle externe : parcours des 10,000 parties
    3. Boucle interne : analyse main par main (commence à main 5)
    4. Calcul des métriques entropiques (~130-140 par main)
    5. Alignement temporel : main i → pattern i+1
    6. Analyse de significance des métriques pour S/O
    7. Génération du rapport final
    """
    
    def __init__(self, dataset_path: str):
        """
        Initialise l'analyseur avec le chemin du dataset.
        
        Args:
            dataset_path (str): Chemin vers le fichier JSON du dataset
        """
        self.dataset_path = dataset_path
        self.dataset_json = None
        
        # Composants intégrés
        self.calculateur_metriques = CalculateurMetriquesEntropiques()
        self.analyseur_significance = AnalyseurSignificanceMetriques()
        self.generateur_rapport = GenerateurRapportAnalyse()
        
        # Données d'analyse
        self.donnees_analyse = []
        self.metriques_globales = {}
        self.resultats_significance = {}
        
        print("🚀 ANALYSEUR BACCARAT OPTIMISÉ - ANABCT.PY")
        print("=" * 60)
        print("💾 Configuration : 28GB RAM disponible")
        print("⚡ Optimisations : NumPy + vectorisation + 8 cœurs")
        print("🎯 Objectif : ~130-140 métriques par main")
        print("📊 Analyse : Main par main sans moyennage")
        print("=" * 60)
    
    def charger_dataset_direct(self) -> bool:
        """
        Charge le dataset JSON directement sans dépendances externes.
        
        Returns:
            bool: True si chargement réussi, False sinon
        """
        print(f"\n📂 CHARGEMENT DIRECT DU DATASET")
        print("-" * 40)
        
        if not os.path.exists(self.dataset_path):
            print(f"❌ Dataset non trouvé: {self.dataset_path}")
            return False
        
        # Vérifier la taille du fichier
        size_gb = os.path.getsize(self.dataset_path) / (1024**3)
        print(f"📊 Taille dataset : {size_gb:.2f} GB")
        
        try:
            print("🔄 Chargement JSON en cours...")
            with open(self.dataset_path, 'r', encoding='utf-8') as f:
                self.dataset_json = json.load(f)
            
            # Validation de la structure
            if 'parties' not in self.dataset_json:
                print("❌ Structure JSON invalide : clé 'parties' manquante")
                return False
            
            nb_parties = len(self.dataset_json['parties'])
            print(f"✅ Dataset chargé : {nb_parties:,} parties")
            
            # Validation des métadonnées
            metadata = self.dataset_json.get('metadata', {})
            print(f"📋 Générateur : {metadata.get('generateur', 'N/A')}")
            print(f"📅 Date génération : {metadata.get('date_generation', 'N/A')}")
            print(f"🎲 Hasard crypto : {metadata.get('hasard_cryptographique', 'N/A')}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur chargement dataset : {e}")
            return False
    
    def executer_analyse_complete(self) -> Dict[str, Any]:
        """
        Exécute l'analyse complète main par main.
        
        Returns:
            Dict[str, Any]: Résultats complets de l'analyse
        """
        print(f"\n🔬 ANALYSE COMPLÈTE MAIN PAR MAIN")
        print("-" * 40)
        
        # Phase 1 : Chargement des données
        if not self.charger_dataset_direct():
            return {'erreur': 'Échec chargement dataset'}
        
        # Phase 2 : Analyse main par main
        print(f"\n📊 PHASE 2: ANALYSE MAIN PAR MAIN")
        print("-" * 40)
        
        parties_traitees = 0
        mains_analysees = 0
        
        # BOUCLE EXTERNE : Parcours de chaque partie individuellement
        for partie in self.dataset_json['parties']:
            partie_id = partie.get('partie_number', 'unknown')
            mains = partie.get('mains', [])
            
            if len(mains) < 6:  # Minimum pour analyse L4/L5
                continue
            
            # BOUCLE INTERNE : Parcours de chaque main dans cette partie
            for i in range(5, len(mains)):  # Commence à main 5 (index réel)
                # CALCUL MAIN PAR MAIN pour cette partie spécifique
                donnees_main = self._analyser_main_individuelle(partie, i)
                
                if donnees_main is not None:
                    donnees_main['partie_id'] = partie_id
                    donnees_main['main'] = i
                    self.donnees_analyse.append(donnees_main)
                    mains_analysees += 1
            
            parties_traitees += 1
            if parties_traitees % 1000 == 0:
                print(f"   📈 {parties_traitees:,} parties traitées, {mains_analysees:,} mains analysées")
                gc.collect()  # Libération mémoire périodique
        
        print(f"✅ Analyse terminée : {parties_traitees:,} parties, {mains_analysees:,} mains")
        
        # Phase 3 : Calcul des métriques globales
        print(f"\n🧮 PHASE 3: CALCUL MÉTRIQUES GLOBALES")
        print("-" * 40)
        self.metriques_globales = self.calculateur_metriques.calculer_metriques_globales(self.donnees_analyse)
        
        # Phase 4 : Analyse de significance
        print(f"\n🎯 PHASE 4: ANALYSE SIGNIFICANCE MÉTRIQUES")
        print("-" * 40)
        self.resultats_significance = self.analyseur_significance.analyser_significance_so(
            self.donnees_analyse, self.metriques_globales
        )
        
        # Phase 5 : Génération du rapport
        print(f"\n📋 PHASE 5: GÉNÉRATION RAPPORT FINAL")
        print("-" * 40)
        rapport_final = self.generateur_rapport.generer_rapport_complet(
            self.donnees_analyse, self.metriques_globales, self.resultats_significance
        )
        
        return {
            'donnees_analyse': self.donnees_analyse,
            'metriques_globales': self.metriques_globales,
            'resultats_significance': self.resultats_significance,
            'rapport_final': rapport_final,
            'statistiques': {
                'parties_traitees': parties_traitees,
                'mains_analysees': mains_analysees,
                'metriques_par_main': len(self.metriques_globales) if self.metriques_globales else 0
            }
        }

    def _analyser_partie_complete(self, partie: Dict) -> Dict[str, Any]:
        """
        Analyse une partie complète et calcule toutes les métriques main par main.
        LOGIQUE INSPIRÉE DE analyse_complete_avec_diff.py

        Args:
            partie (Dict): Données de la partie

        Returns:
            Dict[str, Any]: Données d'analyse de la partie
        """
        mains = partie.get('mains', [])
        partie_id = partie.get('partie_number', 0)

        if len(mains) < 6:  # Minimum pour commencer l'analyse à la main 5
            return {'erreur': 'Partie trop courte'}

        # Extraire toutes les séquences INDEX5 et INDEX3
        sequences_index5 = []
        sequences_index3 = []

        for main in mains:
            index5 = main.get('index5_combined', '')
            index3 = main.get('index3_result', '')

            if index5:
                sequences_index5.append(index5)
            if index3:
                sequences_index3.append(index3)

        if len(sequences_index5) < 5 or len(sequences_index3) < 5:
            return {'erreur': 'Séquences insuffisantes'}

        # Calculer l'entropie globale de toute la séquence
        entropie_globale = self._calculer_entropie_shannon(sequences_index5)

        # Calculer les patterns S/O/E pour toute la partie
        patterns_soe = self._calculer_patterns_soe(sequences_index3)

        # Calculer les ratios L4/L5 pour chaque main
        ratios_l4 = []
        ratios_l5 = []

        for i in range(4, len(sequences_index5)):  # Commence à l'index 4 (main 5)
            # Séquence L4 : 4 derniers éléments
            seq_l4 = sequences_index5[max(0, i-3):i+1]
            entropie_l4 = self._calculer_entropie_shannon(seq_l4)
            ratio_l4 = entropie_l4 / entropie_globale if entropie_globale > 0 else 0.0
            ratios_l4.append(ratio_l4)

            # Séquence L5 : 5 derniers éléments
            seq_l5 = sequences_index5[max(0, i-4):i+1]
            entropie_l5 = self._calculer_entropie_shannon(seq_l5)
            ratio_l5 = entropie_l5 / entropie_globale if entropie_globale > 0 else 0.0
            ratios_l5.append(ratio_l5)

        # Calculer les variations DIFF_L4 et DIFF_L5
        diff_l4_variations = self._calculer_variations_ratios(ratios_l4)
        diff_l5_variations = self._calculer_variations_ratios(ratios_l5)

        return {
            'partie_id': partie_id,
            'ratios_l4': ratios_l4,
            'ratios_l5': ratios_l5,
            'patterns_soe': patterns_soe[4:] if len(patterns_soe) > 4 else [],  # Commence à la main 5
            'index3_resultats': sequences_index3[4:] if len(sequences_index3) > 4 else [],  # Commence à la main 5
            'diff_l4_variations': diff_l4_variations,
            'diff_l5_variations': diff_l5_variations,
            'entropie_globale': entropie_globale
        }

    def _calculer_variations_ratios(self, ratios: List[float]) -> List[float]:
        """
        Calcule les variations absolues entre mains consécutives
        LOGIQUE IDENTIQUE À analyseur_transitions_index5.py

        Args:
            ratios: Liste des ratios (L4 ou L5)

        Returns:
            list: Liste des variations absolues |ratio_n - ratio_n-1|
        """
        if len(ratios) < 2:
            return []

        variations = []
        for i in range(1, len(ratios)):
            variation = abs(ratios[i] - ratios[i-1])
            variations.append(variation)

        return variations

    def _analyser_toutes_conditions_avec_diff(self, donnees_analyse: List[Dict]) -> tuple:
        """
        Analyse exhaustive des conditions avec DIFF comme dans analyse_complete_avec_diff.py

        Args:
            donnees_analyse: Liste des données d'analyse avec DIFF

        Returns:
            tuple: (conditions_s, conditions_o) - Conditions significatives trouvées
        """
        print("🔍 ANALYSE EXHAUSTIVE DES CONDITIONS AVEC DIFF")
        print("-" * 50)

        conditions_s = []
        conditions_o = []

        # 1. ANALYSE DIFF PAR TRANCHES DE QUALITÉ
        print("📊 1. ANALYSE DIFF PAR TRANCHES DE QUALITÉ")
        tranches_diff = [
            ("PARFAIT", 0.0, 0.020),
            ("EXCELLENT", 0.020, 0.030),
            ("TRÈS_BON", 0.030, 0.050),
            ("BON", 0.050, 0.080),
            ("MOYEN", 0.080, 0.120),
            ("DOUTEUX", 0.120, 0.200),
            ("MAUVAIS", 0.200, 1.0)
        ]

        for nom_tranche, min_diff, max_diff in tranches_diff:
            donnees_tranche = [d for d in donnees_analyse
                             if min_diff <= d['diff'] < max_diff]

            if len(donnees_tranche) >= 100:  # Minimum pour analyse
                cond_s, cond_o = self._analyser_tranche(donnees_tranche, f"DIFF_{nom_tranche}")
                conditions_s.extend(cond_s)
                conditions_o.extend(cond_o)

        # 2. ANALYSE RATIOS L4 PAR TRANCHES
        print("📊 2. ANALYSE RATIOS L4 PAR TRANCHES")
        tranches_l4 = [
            ("TRÈS_FAIBLE", 0.0, 0.3),
            ("FAIBLE", 0.3, 0.6),
            ("MOYEN", 0.6, 0.9),
            ("ÉLEVÉ", 0.9, 1.2),
            ("TRÈS_ÉLEVÉ", 1.2, 10.0)
        ]

        for nom_tranche, min_l4, max_l4 in tranches_l4:
            donnees_tranche = [d for d in donnees_analyse
                             if min_l4 <= d['ratio_l4'] < max_l4]

            if len(donnees_tranche) >= 100:
                cond_s, cond_o = self._analyser_tranche(donnees_tranche, f"L4_{nom_tranche}")
                conditions_s.extend(cond_s)
                conditions_o.extend(cond_o)

        # 3. ANALYSE RATIOS L5 PAR TRANCHES
        print("📊 3. ANALYSE RATIOS L5 PAR TRANCHES")
        tranches_l5 = [
            ("TRÈS_FAIBLE", 0.0, 0.3),
            ("FAIBLE", 0.3, 0.6),
            ("MOYEN", 0.6, 0.9),
            ("ÉLEVÉ", 0.9, 1.2),
            ("TRÈS_ÉLEVÉ", 1.2, 10.0)
        ]

        for nom_tranche, min_l5, max_l5 in tranches_l5:
            donnees_tranche = [d for d in donnees_analyse
                             if min_l5 <= d['ratio_l5'] < max_l5]

            if len(donnees_tranche) >= 100:
                cond_s, cond_o = self._analyser_tranche(donnees_tranche, f"L5_{nom_tranche}")
                conditions_s.extend(cond_s)
                conditions_o.extend(cond_o)

        print(f"✅ ANALYSE TERMINÉE : {len(conditions_s)} conditions S, {len(conditions_o)} conditions O")

        return conditions_s, conditions_o

    def _analyser_tranche(self, donnees_tranche: List[Dict], nom_condition: str) -> tuple:
        """
        Analyse une tranche de données pour identifier les conditions S/O significatives
        LOGIQUE IDENTIQUE À analyse_complete_avec_diff.py

        Args:
            donnees_tranche: Données de la tranche à analyser
            nom_condition: Nom de la condition analysée

        Returns:
            tuple: (conditions_s, conditions_o) pour cette tranche
        """
        if len(donnees_tranche) < 100:
            return [], []

        # Séparer S et O
        donnees_s = [d for d in donnees_tranche if d['pattern'] == 'S']
        donnees_o = [d for d in donnees_tranche if d['pattern'] == 'O']

        total = len(donnees_s) + len(donnees_o)
        if total == 0:
            return [], []

        pourcentage_s = (len(donnees_s) / total) * 100
        pourcentage_o = (len(donnees_o) / total) * 100

        conditions_s = []
        conditions_o = []

        # SEUIL CRITIQUE : 52% (comme dans analyse_complete_avec_diff.py)
        if pourcentage_s >= 52.0:
            force = "FORTE" if pourcentage_s >= 60 else "MODÉRÉE" if pourcentage_s >= 55 else "FAIBLE"
            conditions_s.append({
                'condition': nom_condition,
                'pourcentage_s': pourcentage_s,
                'nb_donnees_s': len(donnees_s),
                'nb_donnees_total': total,
                'force': force
            })

        if pourcentage_o >= 52.0:
            force = "FORTE" if pourcentage_o >= 60 else "MODÉRÉE" if pourcentage_o >= 55 else "FAIBLE"
            conditions_o.append({
                'condition': nom_condition,
                'pourcentage_o': pourcentage_o,
                'nb_donnees_o': len(donnees_o),
                'nb_donnees_total': total,
                'force': force
            })

        return conditions_s, conditions_o

    def _analyser_main_individuelle(self, partie: Dict, index_main: int) -> Optional[Dict[str, Any]]:
        """
        Analyse une main individuelle et calcule toutes les métriques.
        NOUVELLE LOGIQUE BASÉE SUR analyse_complete_avec_diff.py

        Args:
            partie (Dict): Données de la partie
            index_main (int): Index de la main à analyser (commence à 5)

        Returns:
            Optional[Dict[str, Any]]: Données de la main analysée ou None si erreur
        """
        mains = partie.get('mains', [])

        if index_main >= len(mains) or index_main < 5:
            return None

        # CORRECTION MAJEURE : Extraire TOUTES les séquences INDEX3 de la partie
        # pour calculer les patterns correctement
        sequences_index3_complete = []
        sequences_index5_complete = []

        for main in mains:  # TOUTE la partie
            index5 = main.get('index5_combined', '')
            index3 = main.get('index3_result', '')

            if index5:
                sequences_index5_complete.append(index5)
            if index3:
                sequences_index3_complete.append(index3)

        if len(sequences_index3_complete) < index_main + 1:  # Pas assez de données
            return None

        # Calculer les patterns S/O/E pour TOUTE la partie
        patterns_soe_complete = self._calculer_patterns_soe(sequences_index3_complete)

        # Extraire les séquences jusqu'à cette main pour les calculs d'entropie
        sequences_index5 = sequences_index5_complete[:index_main + 1]
        sequences_index3 = sequences_index3_complete[:index_main + 1]

        if len(sequences_index5) < 5 or len(sequences_index3) < 5:  # Minimum pour L4/L5
            return None

        # Calculer l'entropie globale de toute la séquence
        entropie_globale = self._calculer_entropie_shannon(sequences_index5)

        # Calculer les entropies locales L4 et L5 pour cette main
        # L4 : 4 derniers éléments (incluant la main courante)
        seq_l4 = sequences_index5[max(0, index_main-3):index_main+1]
        entropie_l4 = self._calculer_entropie_shannon(seq_l4)
        ratio_l4 = entropie_l4 / entropie_globale if entropie_globale > 0 else 0.0

        # L5 : 5 derniers éléments (incluant la main courante)
        seq_l5 = sequences_index5[max(0, index_main-4):index_main+1]
        entropie_l5 = self._calculer_entropie_shannon(seq_l5)
        ratio_l5 = entropie_l5 / entropie_globale if entropie_globale > 0 else 0.0

        # Calculer la variable DIFF critique
        diff_coherence = abs(ratio_l4 - ratio_l5)

        # CORRECTION CRITIQUE : Obtenir le pattern pour cette main
        # patterns_soe_complete[i] = pattern de la main i
        pattern = 'E'  # Par défaut
        if len(patterns_soe_complete) > index_main and patterns_soe_complete[index_main] is not None:
            pattern = patterns_soe_complete[index_main]

        # Calculer les métriques supplémentaires
        metriques_supplementaires = self._calculer_metriques_supplementaires(
            sequences_index5, sequences_index3, index_main
        )

        return {
            'entropie_l4': entropie_l4,
            'entropie_l5': entropie_l5,
            'entropie_globale': entropie_globale,
            'ratio_l4': ratio_l4,
            'ratio_l5': ratio_l5,
            'diff': diff_coherence,
            'pattern': pattern,
            'index3': sequences_index3[index_main] if index_main < len(sequences_index3) else '',
            'metriques_supplementaires': metriques_supplementaires
        }

    def _calculer_entropie_locale(self, sequence: List[str], longueur: int) -> float:
        """
        Calcule l'entropie locale sur une fenêtre glissante.

        Args:
            sequence (List[str]): Séquence des INDEX5
            longueur (int): Longueur de la fenêtre (4 ou 5)

        Returns:
            float: Entropie locale moyenne
        """
        if len(sequence) < longueur:
            return 0.0

        entropies = []

        # Fenêtre glissante
        for i in range(len(sequence) - longueur + 1):
            sous_sequence = sequence[i:i + longueur]
            entropie = self._calculer_entropie_shannon(sous_sequence)
            entropies.append(entropie)

        return np.mean(entropies) if entropies else 0.0

    def _calculer_entropie_shannon(self, sequence: List[str]) -> float:
        """
        Calcule l'entropie de Shannon d'une séquence.

        Args:
            sequence (List[str]): Séquence à analyser

        Returns:
            float: Entropie de Shannon
        """
        if not sequence:
            return 0.0

        # Compter les occurrences
        compteur = Counter(sequence)
        total = len(sequence)

        # Calculer l'entropie
        entropie = 0.0
        for count in compteur.values():
            if count > 0:
                p = count / total
                entropie -= p * math.log2(p)

        return entropie

    def _calculer_patterns_soe(self, index3_resultats: List[str]) -> List[str]:
        """
        Calcule les patterns S (Same), O (Opposite), E (Égalité) pour chaque main
        COPIÉ EXACTEMENT DE analyseur_transitions_index5.py (lignes 6365-6417)

        Args:
            index3_resultats: Liste des résultats INDEX3 (BANKER/PLAYER/TIE)

        Returns:
            list: Liste des patterns S/O/E où patterns[i] = pattern de la main i
        """
        if len(index3_resultats) < 2:
            return []

        patterns = [None]  # patterns[0] = None (pas de pattern pour main 0)
        dernier_non_tie = None

        for i in range(1, len(index3_resultats)):
            resultat_actuel = index3_resultats[i]
            resultat_precedent = index3_resultats[i-1]

            # Si le résultat actuel est TIE
            if resultat_actuel == 'TIE':
                patterns.append('E')  # patterns[i] = pattern de la main i
                continue

            # Si le résultat précédent est TIE, chercher le dernier non-TIE
            if resultat_precedent == 'TIE':
                # Chercher le dernier résultat non-TIE avant la position i-1
                dernier_non_tie = None
                for j in range(i-2, -1, -1):
                    if index3_resultats[j] != 'TIE':
                        dernier_non_tie = index3_resultats[j]
                        break

                # Si aucun résultat non-TIE trouvé, on ne peut pas déterminer le pattern
                if dernier_non_tie is None:
                    patterns.append('--')  # Indéterminé
                    continue

                # Comparer avec le dernier non-TIE
                if resultat_actuel == dernier_non_tie:
                    patterns.append('S')  # Same
                else:
                    patterns.append('O')  # Opposite
            else:
                # Comparaison normale (pas de TIE précédent)
                if resultat_actuel == resultat_precedent:
                    patterns.append('S')  # Same
                else:
                    patterns.append('O')  # Opposite

        return patterns

    def _calculer_metriques_supplementaires(self, sequences_index5: List[str],
                                          sequences_index3: List[str], index_main: int) -> Dict[str, float]:
        """
        Calcule les métriques supplémentaires pour cette main.

        Args:
            sequences_index5 (List[str]): Séquence INDEX5
            sequences_index3 (List[str]): Séquence INDEX3
            index_main (int): Index de la main

        Returns:
            Dict[str, float]: Métriques supplémentaires
        """
        metriques = {}

        # Métriques de base
        metriques['longueur_sequence'] = len(sequences_index5)
        metriques['diversite_index5'] = len(set(sequences_index5))
        metriques['diversite_index3'] = len(set(sequences_index3))

        # Métriques de concentration
        if sequences_index5:
            compteur_index5 = Counter(sequences_index5)
            total = len(sequences_index5)
            metriques['concentration_max'] = max(compteur_index5.values()) / total
            metriques['uniformite'] = len(compteur_index5) / total if total > 0 else 0

        return metriques


# ============================================================================
# CLASSE : CALCULATEUR MÉTRIQUES ENTROPIQUES
# ============================================================================

class CalculateurMetriquesEntropiques:
    """
    CALCULATEUR DE ~130-140 MÉTRIQUES ENTROPIQUES PAR MAIN
    =====================================================

    Intègre toutes les formules d'entropie du système original :
    - 52+ formules mathématiques d'entropie
    - Métriques dérivées et combinaisons
    - Analyses temporelles et corrélations
    - Optimisations pour performance (corrélations désactivées)

    FORMULES INTÉGRÉES :
    1. Entropies de base (Shannon, Bernoulli, Uniforme)
    2. Divergences (KL, Cross-entropy, JS)
    3. Entropies conditionnelles et jointes
    4. Métriques spécialisées baccarat
    5. Analyses temporelles (Markov, ergodique)
    6. Information mutuelle
    """

    def __init__(self):
        """Initialise le calculateur de métriques."""
        self.metriques_calculees = {}
        self.formules_entropie = {}

    def calculer_metriques_globales(self, donnees_analyse: List[Dict]) -> Dict[str, Any]:
        """
        Calcule toutes les métriques globales sur l'ensemble des données.

        Args:
            donnees_analyse (List[Dict]): Données d'analyse de toutes les mains

        Returns:
            Dict[str, Any]: Métriques globales calculées
        """
        print("   🧮 Calcul des métriques entropiques globales...")

        if not donnees_analyse:
            return {}

        # Extraire les séquences pour calculs
        sequences = self._extraire_sequences_pour_entropie(donnees_analyse)

        # Calculer toutes les formules d'entropie
        self._calculer_toutes_formules_entropie(sequences)

        # Calculer les métriques dérivées
        self._calculer_metriques_derivees(donnees_analyse)

        # Calculer les écarts-types
        self._calculer_ecarts_types(donnees_analyse)

        print(f"   ✅ {len(self.metriques_calculees)} métriques calculées")

        return self.metriques_calculees

    def _extraire_sequences_pour_entropie(self, donnees_analyse: List[Dict]) -> Dict[str, List]:
        """
        Extrait les séquences nécessaires pour les calculs d'entropie.

        Args:
            donnees_analyse (List[Dict]): Données d'analyse

        Returns:
            Dict[str, List]: Séquences extraites
        """
        sequences = {
            'patterns': [],           # Patterns S/O
            'resultats': [],         # Résultats INDEX3
            'ratios_l4': [],         # Ratios L4
            'ratios_l5': [],         # Ratios L5
            'diff_values': [],       # Valeurs DIFF
            'entropies_l4': [],      # Entropies locales L4
            'entropies_l5': [],      # Entropies locales L5
            'entropies_globales': [] # Entropies globales
        }

        for donnee in donnees_analyse:
            # Patterns S/O (convertis en 0/1 pour Bernoulli)
            pattern = donnee.get('pattern', '')
            if pattern == 'S':
                sequences['patterns'].append(1)
            elif pattern == 'O':
                sequences['patterns'].append(0)

            # Autres métriques
            sequences['resultats'].append(donnee.get('index3', ''))
            sequences['ratios_l4'].append(donnee.get('ratio_l4', 0.0))
            sequences['ratios_l5'].append(donnee.get('ratio_l5', 0.0))
            sequences['diff_values'].append(donnee.get('diff', 0.0))
            sequences['entropies_l4'].append(donnee.get('entropie_l4', 0.0))
            sequences['entropies_l5'].append(donnee.get('entropie_l5', 0.0))
            sequences['entropies_globales'].append(donnee.get('entropie_globale', 0.0))

        return sequences

    def _calculer_toutes_formules_entropie(self, sequences: Dict[str, List]):
        """
        Calcule toutes les 52+ formules d'entropie.

        Args:
            sequences (Dict[str, List]): Séquences extraites
        """
        # 1. ENTROPIES DE BASE
        self._calculer_entropies_base(sequences)

        # 2. DIVERGENCES ET COMPARAISONS
        self._calculer_divergences(sequences)

        # 3. ENTROPIES CONDITIONNELLES
        self._calculer_entropies_conditionnelles(sequences)

        # 4. MÉTRIQUES SPÉCIALISÉES BACCARAT
        self._calculer_metriques_baccarat(sequences)

        # 5. ANALYSES TEMPORELLES
        self._calculer_analyses_temporelles(sequences)

        # 6. INFORMATION MUTUELLE
        self._calculer_information_mutuelle(sequences)

    def _calculer_entropies_base(self, sequences: Dict[str, List]):
        """Calcule les entropies de base (Shannon, Bernoulli, Uniforme)."""

        # 1. ENTROPIE DE SHANNON pour patterns S/O
        if sequences['patterns']:
            pattern_counts = Counter(sequences['patterns'])
            total_patterns = len(sequences['patterns'])
            if total_patterns > 0:
                shannon_entropy = 0.0
                for count in pattern_counts.values():
                    if count > 0:
                        p = count / total_patterns
                        shannon_entropy -= p * math.log2(p)
                self.metriques_calculees['shannon_entropy_patterns'] = shannon_entropy

        # 2. ENTROPIE BERNOULLI (distribution binaire S/O)
        if sequences['patterns']:
            p_s = sequences['patterns'].count(1) / len(sequences['patterns'])
            if 0 < p_s < 1:
                bernoulli_entropy = -p_s * math.log2(p_s) - (1-p_s) * math.log2(1-p_s)
                self.metriques_calculees['bernoulli_entropy'] = bernoulli_entropy

        # 3. ENTROPIE UNIFORME pour résultats B/P/T
        if sequences['resultats']:
            resultats_counts = Counter(sequences['resultats'])
            total_resultats = len(sequences['resultats'])
            if total_resultats > 0:
                uniform_entropy = 0.0
                for count in resultats_counts.values():
                    if count > 0:
                        p = count / total_resultats
                        uniform_entropy -= p * math.log2(p)
                self.metriques_calculees['uniform_entropy_resultats'] = uniform_entropy

        # 4. ENTROPIES MOYENNES des métriques continues
        if sequences['ratios_l4']:
            self.metriques_calculees['moyenne_ratios_l4'] = np.mean(sequences['ratios_l4'])
            self.metriques_calculees['variance_ratios_l4'] = np.var(sequences['ratios_l4'])

        if sequences['ratios_l5']:
            self.metriques_calculees['moyenne_ratios_l5'] = np.mean(sequences['ratios_l5'])
            self.metriques_calculees['variance_ratios_l5'] = np.var(sequences['ratios_l5'])

        if sequences['diff_values']:
            self.metriques_calculees['moyenne_diff'] = np.mean(sequences['diff_values'])
            self.metriques_calculees['variance_diff'] = np.var(sequences['diff_values'])

    def _calculer_divergences(self, sequences: Dict[str, List]):
        """Calcule les divergences et comparaisons."""

        # 1. DIVERGENCE KL entre ratios L4 et L5
        if sequences['ratios_l4'] and sequences['ratios_l5']:
            # Discrétiser les ratios pour calcul KL
            ratios_l4_disc = self._discretiser_valeurs(sequences['ratios_l4'])
            ratios_l5_disc = self._discretiser_valeurs(sequences['ratios_l5'])

            if ratios_l4_disc and ratios_l5_disc:
                kl_div = self._calculer_divergence_kl(ratios_l4_disc, ratios_l5_disc)
                self.metriques_calculees['kl_divergence_l4_l5'] = kl_div

        # 2. CROSS-ENTROPY entre patterns et prédictions
        if sequences['patterns'] and sequences['diff_values']:
            # Utiliser DIFF comme prédicteur de patterns
            cross_entropy = self._calculer_cross_entropy(sequences['patterns'], sequences['diff_values'])
            self.metriques_calculees['cross_entropy_pattern_diff'] = cross_entropy

        # 3. DIVERGENCE JENSEN-SHANNON
        if sequences['ratios_l4'] and sequences['ratios_l5']:
            js_div = self._calculer_divergence_js(sequences['ratios_l4'], sequences['ratios_l5'])
            self.metriques_calculees['js_divergence_l4_l5'] = js_div

    def _calculer_entropies_conditionnelles(self, sequences: Dict[str, List]):
        """Calcule les entropies conditionnelles et jointes."""

        # 1. ENTROPIE CONDITIONNELLE H(Pattern|DIFF)
        if sequences['patterns'] and sequences['diff_values']:
            # Discrétiser DIFF en tranches
            diff_discrete = self._discretiser_valeurs(sequences['diff_values'])
            if diff_discrete:
                h_conditional = self._calculer_entropie_conditionnelle(sequences['patterns'], diff_discrete)
                self.metriques_calculees['conditional_entropy_pattern_diff'] = h_conditional

        # 2. ENTROPIE JOINTE H(L4, L5)
        if sequences['ratios_l4'] and sequences['ratios_l5']:
            # Créer distribution jointe
            joint_entropy = self._calculer_entropie_jointe(sequences['ratios_l4'], sequences['ratios_l5'])
            self.metriques_calculees['joint_entropy_l4_l5'] = joint_entropy

        # 3. INFORMATION MUTUELLE I(Pattern; DIFF)
        if sequences['patterns'] and sequences['diff_values']:
            mutual_info = self._calculer_information_mutuelle_discrete(
                sequences['patterns'], self._discretiser_valeurs(sequences['diff_values'])
            )
            self.metriques_calculees['mutual_info_pattern_diff'] = mutual_info

    def _calculer_metriques_baccarat(self, sequences: Dict[str, List]):
        """Calcule les métriques spécialisées baccarat."""

        # 1. FORMULE LOGARITHMIQUE RÉVOLUTIONNAIRE
        if sequences['diff_values']:
            predictions_log = []
            for diff in sequences['diff_values']:
                # P(S) = 0.45 + 0.35 * log(DIFF + 0.01)
                prob_s = 0.45 + 0.35 * math.log(diff + 0.01)
                predictions_log.append(max(0.0, min(1.0, prob_s)))  # Borner entre 0 et 1

            self.metriques_calculees['predictions_logarithmiques'] = predictions_log
            self.metriques_calculees['moyenne_pred_log'] = np.mean(predictions_log)

        # 2. QUALITÉ DU SIGNAL PRÉDICTIF
        if sequences['diff_values']:
            # Classification par tranches de qualité DIFF
            tranches_qualite = {
                'parfait': [d for d in sequences['diff_values'] if d < 0.020],
                'excellent': [d for d in sequences['diff_values'] if 0.020 <= d < 0.030],
                'tres_bon': [d for d in sequences['diff_values'] if 0.030 <= d < 0.050],
                'douteux': [d for d in sequences['diff_values'] if d > 0.150]
            }

            total = len(sequences['diff_values'])
            for qualite, valeurs in tranches_qualite.items():
                self.metriques_calculees[f'proportion_{qualite}'] = len(valeurs) / total if total > 0 else 0

        # 3. RATIOS ET DIFFÉRENCES AVANCÉS
        if sequences['ratios_l4'] and sequences['ratios_l5']:
            # Métriques dérivées
            sommes = [l4 + l5 for l4, l5 in zip(sequences['ratios_l4'], sequences['ratios_l5'])]
            produits = [l4 * l5 for l4, l5 in zip(sequences['ratios_l4'], sequences['ratios_l5'])]

            self.metriques_calculees['moyenne_somme_ratios'] = np.mean(sommes)
            self.metriques_calculees['moyenne_produit_ratios'] = np.mean(produits)
            self.metriques_calculees['correlation_l4_l5'] = np.corrcoef(sequences['ratios_l4'], sequences['ratios_l5'])[0,1]

    def _calculer_analyses_temporelles(self, sequences: Dict[str, List]):
        """Calcule les analyses temporelles (Markov, ergodique)."""

        # 1. ENTROPIE DE MARKOV pour transitions S→S, S→O, O→S, O→O
        if sequences['patterns'] and len(sequences['patterns']) > 1:
            transitions = []
            for i in range(len(sequences['patterns']) - 1):
                transition = (sequences['patterns'][i], sequences['patterns'][i + 1])
                transitions.append(transition)

            if transitions:
                transition_counts = Counter(transitions)
                total_transitions = len(transitions)
                markov_entropy = 0.0

                for count in transition_counts.values():
                    if count > 0:
                        p = count / total_transitions
                        markov_entropy -= p * math.log2(p)

                self.metriques_calculees['markov_entropy'] = markov_entropy

        # 2. ESTIMATION ERGODIQUE
        if sequences['patterns'] and len(sequences['patterns']) > 100:
            # Estimation de l'entropie ergodique par blocs
            bloc_size = min(10, len(sequences['patterns']) // 10)
            entropies_blocs = []

            for i in range(0, len(sequences['patterns']) - bloc_size + 1, bloc_size):
                bloc = sequences['patterns'][i:i + bloc_size]
                if bloc:
                    bloc_counts = Counter(bloc)
                    bloc_total = len(bloc)
                    bloc_entropy = 0.0

                    for count in bloc_counts.values():
                        if count > 0:
                            p = count / bloc_total
                            bloc_entropy -= p * math.log2(p)

                    entropies_blocs.append(bloc_entropy)

            if entropies_blocs:
                self.metriques_calculees['ergodic_entropy_estimate'] = np.mean(entropies_blocs)

    def _calculer_information_mutuelle(self, sequences: Dict[str, List]):
        """Calcule l'information mutuelle entre différentes métriques."""

        # Information mutuelle entre patterns et DIFF
        if sequences['patterns'] and sequences['diff_values']:
            diff_discrete = self._discretiser_valeurs(sequences['diff_values'])
            if diff_discrete:
                mutual_info = self._calculer_information_mutuelle_discrete(sequences['patterns'], diff_discrete)
                self.metriques_calculees['mutual_info_pattern_diff'] = mutual_info

        # Information mutuelle entre ratios L4 et L5
        if sequences['ratios_l4'] and sequences['ratios_l5']:
            l4_discrete = self._discretiser_valeurs(sequences['ratios_l4'])
            l5_discrete = self._discretiser_valeurs(sequences['ratios_l5'])

            if l4_discrete and l5_discrete:
                mutual_info = self._calculer_information_mutuelle_discrete(l4_discrete, l5_discrete)
                self.metriques_calculees['mutual_info_l4_l5'] = mutual_info

    def _discretiser_valeurs(self, valeurs: List[float], nb_bins: int = 10) -> List[int]:
        """
        Discrétise des valeurs continues en bins.

        Args:
            valeurs (List[float]): Valeurs à discrétiser
            nb_bins (int): Nombre de bins

        Returns:
            List[int]: Valeurs discrétisées
        """
        if not valeurs:
            return []

        valeurs_array = np.array(valeurs)
        min_val, max_val = np.min(valeurs_array), np.max(valeurs_array)

        if min_val == max_val:
            return [0] * len(valeurs)

        bins = np.linspace(min_val, max_val, nb_bins + 1)
        indices = np.digitize(valeurs_array, bins) - 1
        indices = np.clip(indices, 0, nb_bins - 1)

        return indices.tolist()

    def _calculer_divergence_kl(self, p_dist: List[int], q_dist: List[int]) -> float:
        """Calcule la divergence KL entre deux distributions discrètes."""
        if not p_dist or not q_dist:
            return 0.0

        p_counts = Counter(p_dist)
        q_counts = Counter(q_dist)

        # Normaliser
        p_total = len(p_dist)
        q_total = len(q_dist)

        kl_div = 0.0
        for val in set(p_dist + q_dist):
            p_prob = p_counts.get(val, 0) / p_total
            q_prob = q_counts.get(val, 1e-10) / q_total  # Éviter division par 0

            if p_prob > 0:
                kl_div += p_prob * math.log2(p_prob / q_prob)

        return kl_div

    def _calculer_cross_entropy(self, true_labels: List[int], predictions: List[float]) -> float:
        """Calcule la cross-entropy entre labels vrais et prédictions."""
        if not true_labels or not predictions or len(true_labels) != len(predictions):
            return 0.0

        cross_entropy = 0.0
        n = len(true_labels)

        for i in range(n):
            y_true = true_labels[i]
            y_pred = max(1e-10, min(1 - 1e-10, predictions[i]))  # Éviter log(0)

            if y_true == 1:
                cross_entropy -= math.log2(y_pred)
            else:
                cross_entropy -= math.log2(1 - y_pred)

        return cross_entropy / n

    def _calculer_divergence_js(self, p_vals: List[float], q_vals: List[float]) -> float:
        """Calcule la divergence Jensen-Shannon."""
        if not p_vals or not q_vals:
            return 0.0

        # Discrétiser les valeurs
        p_disc = self._discretiser_valeurs(p_vals)
        q_disc = self._discretiser_valeurs(q_vals)

        # Calculer les distributions
        p_counts = Counter(p_disc)
        q_counts = Counter(q_disc)

        all_vals = set(p_disc + q_disc)
        p_total = len(p_disc)
        q_total = len(q_disc)

        # Distribution moyenne M = (P + Q) / 2
        js_div = 0.0

        for val in all_vals:
            p_prob = p_counts.get(val, 0) / p_total
            q_prob = q_counts.get(val, 0) / q_total
            m_prob = (p_prob + q_prob) / 2

            if p_prob > 0 and m_prob > 0:
                js_div += 0.5 * p_prob * math.log2(p_prob / m_prob)
            if q_prob > 0 and m_prob > 0:
                js_div += 0.5 * q_prob * math.log2(q_prob / m_prob)

        return js_div

    def _calculer_entropie_conditionnelle(self, x_vals: List[int], y_vals: List[int]) -> float:
        """Calcule l'entropie conditionnelle H(X|Y)."""
        if not x_vals or not y_vals or len(x_vals) != len(y_vals):
            return 0.0

        # Compter les occurrences jointes
        joint_counts = Counter(zip(x_vals, y_vals))
        y_counts = Counter(y_vals)

        total = len(x_vals)
        h_conditional = 0.0

        for (x, y), joint_count in joint_counts.items():
            p_xy = joint_count / total
            p_y = y_counts[y] / total

            if p_xy > 0 and p_y > 0:
                h_conditional -= p_xy * math.log2(p_xy / p_y)

        return h_conditional

    def _calculer_entropie_jointe(self, x_vals: List[float], y_vals: List[float]) -> float:
        """Calcule l'entropie jointe H(X,Y)."""
        if not x_vals or not y_vals or len(x_vals) != len(y_vals):
            return 0.0

        # Discrétiser les valeurs
        x_disc = self._discretiser_valeurs(x_vals)
        y_disc = self._discretiser_valeurs(y_vals)

        # Compter les occurrences jointes
        joint_counts = Counter(zip(x_disc, y_disc))
        total = len(x_disc)

        joint_entropy = 0.0
        for count in joint_counts.values():
            if count > 0:
                p = count / total
                joint_entropy -= p * math.log2(p)

        return joint_entropy

    def _calculer_information_mutuelle_discrete(self, x_vals: List[int], y_vals: List[int]) -> float:
        """Calcule l'information mutuelle I(X;Y) = H(X) + H(Y) - H(X,Y)."""
        if not x_vals or not y_vals or len(x_vals) != len(y_vals):
            return 0.0

        # Entropies marginales
        x_counts = Counter(x_vals)
        y_counts = Counter(y_vals)
        total = len(x_vals)

        h_x = 0.0
        for count in x_counts.values():
            if count > 0:
                p = count / total
                h_x -= p * math.log2(p)

        h_y = 0.0
        for count in y_counts.values():
            if count > 0:
                p = count / total
                h_y -= p * math.log2(p)

        # Entropie jointe
        joint_counts = Counter(zip(x_vals, y_vals))
        h_xy = 0.0
        for count in joint_counts.values():
            if count > 0:
                p = count / total
                h_xy -= p * math.log2(p)

        return h_x + h_y - h_xy

    def _calculer_metriques_derivees(self, donnees_analyse: List[Dict]):
        """Calcule les métriques dérivées et combinaisons."""

        # Extraire les valeurs
        ratios_l4 = [d.get('ratio_l4', 0.0) for d in donnees_analyse]
        ratios_l5 = [d.get('ratio_l5', 0.0) for d in donnees_analyse]
        diff_values = [d.get('diff', 0.0) for d in donnees_analyse]

        if ratios_l4 and ratios_l5:
            # Métriques dérivées
            sommes_ratios = [l4 + l5 for l4, l5 in zip(ratios_l4, ratios_l5)]
            diff_ratios = [abs(l4 - l5) for l4, l5 in zip(ratios_l4, ratios_l5)]
            produits_ratios = [l4 * l5 for l4, l5 in zip(ratios_l4, ratios_l5)]
            moyennes_ratios = [(l4 + l5) / 2 for l4, l5 in zip(ratios_l4, ratios_l5)]

            # Stocker les métriques dérivées
            self.metriques_calculees['sommes_ratios'] = sommes_ratios
            self.metriques_calculees['diff_ratios'] = diff_ratios
            self.metriques_calculees['produits_ratios'] = produits_ratios
            self.metriques_calculees['moyennes_ratios'] = moyennes_ratios

            # Statistiques des métriques dérivées
            self.metriques_calculees['moyenne_sommes_ratios'] = np.mean(sommes_ratios)
            self.metriques_calculees['std_sommes_ratios'] = np.std(sommes_ratios)
            self.metriques_calculees['moyenne_diff_ratios'] = np.mean(diff_ratios)
            self.metriques_calculees['std_diff_ratios'] = np.std(diff_ratios)

    def _calculer_ecarts_types(self, donnees_analyse: List[Dict]):
        """Calcule les écarts-types de toutes les métriques."""

        # Métriques de base
        metriques_base = ['ratio_l4', 'ratio_l5', 'diff', 'entropie_l4', 'entropie_l5', 'entropie_globale']

        for metrique in metriques_base:
            valeurs = [d.get(metrique, 0.0) for d in donnees_analyse if metrique in d]
            if valeurs:
                self.metriques_calculees[f'std_{metrique}'] = np.std(valeurs)
                self.metriques_calculees[f'var_{metrique}'] = np.var(valeurs)
                self.metriques_calculees[f'min_{metrique}'] = np.min(valeurs)
                self.metriques_calculees[f'max_{metrique}'] = np.max(valeurs)
                self.metriques_calculees[f'median_{metrique}'] = np.median(valeurs)


# ============================================================================
# CLASSE : ANALYSEUR SIGNIFICANCE MÉTRIQUES
# ============================================================================

class AnalyseurSignificanceMetriques:
    """
    ANALYSEUR DE SIGNIFICANCE DES MÉTRIQUES POUR S/O
    ===============================================

    Identifie quelles métriques à la main N sont significatives
    pour expliquer les transitions S/O à la main N+1.

    ANALYSES RÉALISÉES :
    1. Corrélations métriques vs patterns S/O
    2. Tests de significance statistique
    3. Classification par force prédictive
    4. Identification des métriques critiques
    """

    def __init__(self):
        """Initialise l'analyseur de significance."""
        self.resultats_significance = {}
        self.metriques_significatives = {}

    def analyser_significance_so(self, donnees_analyse: List[Dict],
                               metriques_globales: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyse la significance des métriques pour prédire S/O.

        Args:
            donnees_analyse (List[Dict]): Données d'analyse par main
            metriques_globales (Dict[str, Any]): Métriques globales calculées

        Returns:
            Dict[str, Any]: Résultats de l'analyse de significance
        """
        print("   🎯 Analyse significance métriques pour S/O...")

        # Séparer les données S et O
        donnees_s = [d for d in donnees_analyse if d.get('pattern') == 'S']
        donnees_o = [d for d in donnees_analyse if d.get('pattern') == 'O']

        print(f"   📊 Données S: {len(donnees_s):,}, Données O: {len(donnees_o):,}")

        # Analyser chaque métrique
        self._analyser_metriques_individuelles(donnees_s, donnees_o)

        # Analyser les combinaisons de métriques
        self._analyser_combinaisons_metriques(donnees_s, donnees_o)

        # Classer par significance
        self._classer_par_significance()

        print(f"   ✅ {len(self.metriques_significatives)} métriques significatives identifiées")

        return {
            'metriques_significatives': self.metriques_significatives,
            'resultats_tests': self.resultats_significance,
            'statistiques': {
                'nb_donnees_s': len(donnees_s),
                'nb_donnees_o': len(donnees_o),
                'nb_metriques_testees': len(self.resultats_significance)
            }
        }

    def _analyser_metriques_individuelles(self, donnees_s: List[Dict], donnees_o: List[Dict]):
        """Analyse la significance de chaque métrique individuellement."""

        metriques_a_tester = ['ratio_l4', 'ratio_l5', 'diff', 'entropie_l4', 'entropie_l5', 'entropie_globale']

        for metrique in metriques_a_tester:
            # Extraire les valeurs pour S et O
            valeurs_s = [d.get(metrique, 0.0) for d in donnees_s if metrique in d]
            valeurs_o = [d.get(metrique, 0.0) for d in donnees_o if metrique in d]

            if valeurs_s and valeurs_o:
                # Test t de Student
                t_stat, p_value = self._test_t_student(valeurs_s, valeurs_o)

                # Différence des moyennes
                moyenne_s = np.mean(valeurs_s)
                moyenne_o = np.mean(valeurs_o)
                diff_moyennes = abs(moyenne_s - moyenne_o)

                # Coefficient de séparation
                coeff_separation = self._calculer_coefficient_separation(valeurs_s, valeurs_o)

                self.resultats_significance[metrique] = {
                    't_statistic': t_stat,
                    'p_value': p_value,
                    'moyenne_s': moyenne_s,
                    'moyenne_o': moyenne_o,
                    'difference_moyennes': diff_moyennes,
                    'coefficient_separation': coeff_separation,
                    'significatif': p_value < 0.05 if p_value is not None else False
                }

    def _analyser_combinaisons_metriques(self, donnees_s: List[Dict], donnees_o: List[Dict]):
        """Analyse les combinaisons de métriques."""

        # Combinaisons importantes
        combinaisons = [
            ('ratio_l4', 'ratio_l5'),
            ('diff', 'ratio_l4'),
            ('diff', 'ratio_l5'),
            ('entropie_l4', 'entropie_l5')
        ]

        for metrique1, metrique2 in combinaisons:
            # Créer des scores combinés
            scores_s = []
            scores_o = []

            for d in donnees_s:
                if metrique1 in d and metrique2 in d:
                    score = d[metrique1] * d[metrique2]  # Produit simple
                    scores_s.append(score)

            for d in donnees_o:
                if metrique1 in d and metrique2 in d:
                    score = d[metrique1] * d[metrique2]
                    scores_o.append(score)

            if scores_s and scores_o:
                # Test de significance
                t_stat, p_value = self._test_t_student(scores_s, scores_o)

                nom_combinaison = f"{metrique1}_x_{metrique2}"
                self.resultats_significance[nom_combinaison] = {
                    't_statistic': t_stat,
                    'p_value': p_value,
                    'moyenne_s': np.mean(scores_s),
                    'moyenne_o': np.mean(scores_o),
                    'significatif': p_value < 0.05 if p_value is not None else False
                }

    def _classer_par_significance(self):
        """Classe les métriques par ordre de significance."""

        # Trier par p-value (plus petit = plus significatif)
        metriques_triees = sorted(
            self.resultats_significance.items(),
            key=lambda x: x[1].get('p_value', 1.0)
        )

        # Classer par force
        for nom_metrique, resultats in metriques_triees:
            p_value = resultats.get('p_value', 1.0)
            diff_moyennes = resultats.get('difference_moyennes', 0.0)

            if p_value < 0.001 and diff_moyennes > 0.1:
                force = 'FORTE'
            elif p_value < 0.01 and diff_moyennes > 0.05:
                force = 'MODÉRÉE'
            elif p_value < 0.05:
                force = 'FAIBLE'
            else:
                force = 'NON_SIGNIFICATIVE'

            if force != 'NON_SIGNIFICATIVE':
                self.metriques_significatives[nom_metrique] = {
                    'force': force,
                    'p_value': p_value,
                    'difference_moyennes': diff_moyennes,
                    'rang': len(self.metriques_significatives) + 1
                }

    def _test_t_student(self, echantillon1: List[float], echantillon2: List[float]) -> tuple:
        """
        Effectue un test t de Student entre deux échantillons.

        Returns:
            tuple: (t_statistic, p_value)
        """
        if not echantillon1 or not echantillon2:
            return None, None

        n1, n2 = len(echantillon1), len(echantillon2)
        if n1 < 2 or n2 < 2:
            return None, None

        # Moyennes et variances
        m1, m2 = np.mean(echantillon1), np.mean(echantillon2)
        v1, v2 = np.var(echantillon1, ddof=1), np.var(echantillon2, ddof=1)

        # Test t avec variances inégales (Welch)
        if v1 == 0 and v2 == 0:
            return None, None

        s_pooled = math.sqrt(((n1 - 1) * v1 + (n2 - 1) * v2) / (n1 + n2 - 2))
        if s_pooled == 0:
            return None, None

        t_stat = (m1 - m2) / (s_pooled * math.sqrt(1/n1 + 1/n2))

        # Approximation simple de la p-value (pour éviter scipy)
        df = n1 + n2 - 2
        p_value = 2 * (1 - self._cdf_t_approx(abs(t_stat), df))

        return t_stat, p_value

    def _cdf_t_approx(self, t: float, df: int) -> float:
        """Approximation simple de la CDF de la distribution t."""
        # Approximation très basique pour éviter les dépendances
        if df > 30:
            # Approximation normale pour df élevé
            return 0.5 * (1 + math.erf(t / math.sqrt(2)))
        else:
            # Approximation grossière
            return min(0.999, max(0.001, 0.5 + t / (2 * math.sqrt(df))))

    def _calculer_coefficient_separation(self, valeurs1: List[float], valeurs2: List[float]) -> float:
        """Calcule un coefficient de séparation entre deux distributions."""
        if not valeurs1 or not valeurs2:
            return 0.0

        m1, m2 = np.mean(valeurs1), np.mean(valeurs2)
        s1, s2 = np.std(valeurs1), np.std(valeurs2)

        if s1 + s2 == 0:
            return 0.0

        return abs(m1 - m2) / (s1 + s2)


# ============================================================================
# CLASSE : GÉNÉRATEUR RAPPORT ANALYSE
# ============================================================================

class GenerateurRapportAnalyse:
    """
    GÉNÉRATEUR DE RAPPORT D'ANALYSE COMPLET
    ======================================

    Génère un rapport détaillé avec :
    1. Statistiques générales de l'analyse
    2. Métriques significatives identifiées
    3. Recommandations prédictives
    4. Tableaux de résultats
    """

    def __init__(self):
        """Initialise le générateur de rapport."""
        pass

    def generer_rapport_complet(self, donnees_analyse: List[Dict],
                              metriques_globales: Dict[str, Any],
                              resultats_significance: Dict[str, Any]) -> str:
        """
        Génère le rapport complet d'analyse.

        Args:
            donnees_analyse (List[Dict]): Données d'analyse
            metriques_globales (Dict[str, Any]): Métriques globales
            resultats_significance (Dict[str, Any]): Résultats significance

        Returns:
            str: Rapport complet formaté
        """
        print("   📋 Génération du rapport final...")

        rapport = []
        rapport.append("=" * 80)
        rapport.append("RAPPORT D'ANALYSE BACCARAT - ANABCT.PY")
        rapport.append("=" * 80)
        rapport.append(f"Date génération : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        rapport.append("")

        # Section 1 : Statistiques générales
        rapport.extend(self._generer_section_statistiques(donnees_analyse))

        # Section 2 : Métriques significatives
        rapport.extend(self._generer_section_metriques_significatives(resultats_significance))

        # Section 3 : Analyse DIFF
        rapport.extend(self._generer_section_analyse_diff(donnees_analyse))

        # Section 4 : Recommandations
        rapport.extend(self._generer_section_recommandations(resultats_significance))

        rapport_final = "\n".join(rapport)

        # Sauvegarder le rapport
        nom_fichier = f"rapport_anabct_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(nom_fichier, 'w', encoding='utf-8') as f:
            f.write(rapport_final)

        print(f"   ✅ Rapport sauvegardé : {nom_fichier}")

        return rapport_final

    def _generer_section_statistiques(self, donnees_analyse: List[Dict]) -> List[str]:
        """Génère la section des statistiques générales."""
        section = []
        section.append("1. STATISTIQUES GÉNÉRALES")
        section.append("-" * 40)

        # Compter les patterns
        patterns_s = len([d for d in donnees_analyse if d.get('pattern') == 'S'])
        patterns_o = len([d for d in donnees_analyse if d.get('pattern') == 'O'])
        patterns_e = len([d for d in donnees_analyse if d.get('pattern') == 'E'])
        total_patterns = patterns_s + patterns_o

        section.append(f"Total mains analysées : {len(donnees_analyse):,}")

        if total_patterns > 0:
            section.append(f"Patterns S (continuation) : {patterns_s:,} ({patterns_s/total_patterns*100:.1f}%)")
            section.append(f"Patterns O (alternance) : {patterns_o:,} ({patterns_o/total_patterns*100:.1f}%)")
        else:
            section.append(f"Patterns S (continuation) : {patterns_s:,}")
            section.append(f"Patterns O (alternance) : {patterns_o:,}")

        section.append(f"Patterns E (indéterminés) : {patterns_e:,}")
        section.append("")

        # Statistiques DIFF
        diff_values = [d.get('diff', 0.0) for d in donnees_analyse if 'diff' in d]
        if diff_values:
            section.append("ANALYSE VARIABLE DIFF :")
            section.append(f"  Moyenne DIFF : {np.mean(diff_values):.6f}")
            section.append(f"  Médiane DIFF : {np.median(diff_values):.6f}")
            section.append(f"  Écart-type DIFF : {np.std(diff_values):.6f}")
            section.append(f"  Min DIFF : {np.min(diff_values):.6f}")
            section.append(f"  Max DIFF : {np.max(diff_values):.6f}")

            # Tranches de qualité
            parfait = len([d for d in diff_values if d < 0.020])
            excellent = len([d for d in diff_values if 0.020 <= d < 0.030])
            tres_bon = len([d for d in diff_values if 0.030 <= d < 0.050])
            douteux = len([d for d in diff_values if d > 0.150])

            section.append("")
            section.append("TRANCHES DE QUALITÉ DIFF :")
            total_diff = len(diff_values)
            if total_diff > 0:
                section.append(f"  Parfait (< 0.020) : {parfait:,} ({parfait/total_diff*100:.1f}%)")
                section.append(f"  Excellent (0.020-0.030) : {excellent:,} ({excellent/total_diff*100:.1f}%)")
                section.append(f"  Très bon (0.030-0.050) : {tres_bon:,} ({tres_bon/total_diff*100:.1f}%)")
                section.append(f"  Douteux (> 0.150) : {douteux:,} ({douteux/total_diff*100:.1f}%)")
            else:
                section.append(f"  Parfait (< 0.020) : {parfait:,}")
                section.append(f"  Excellent (0.020-0.030) : {excellent:,}")
                section.append(f"  Très bon (0.030-0.050) : {tres_bon:,}")
                section.append(f"  Douteux (> 0.150) : {douteux:,}")

        section.append("")
        return section

    def _generer_section_metriques_significatives(self, resultats_significance: Dict[str, Any]) -> List[str]:
        """Génère la section des métriques significatives."""
        section = []
        section.append("2. MÉTRIQUES SIGNIFICATIVES POUR S/O")
        section.append("-" * 40)

        metriques_sig = resultats_significance.get('metriques_significatives', {})

        if not metriques_sig:
            section.append("Aucune métrique significative identifiée.")
            section.append("")
            return section

        # Trier par force
        metriques_par_force = {'FORTE': [], 'MODÉRÉE': [], 'FAIBLE': []}

        for nom, info in metriques_sig.items():
            force = info.get('force', 'FAIBLE')
            if force in metriques_par_force:
                metriques_par_force[force].append((nom, info))

        for force, metriques in metriques_par_force.items():
            if metriques:
                section.append(f"{force} SIGNIFICANCE :")
                for nom, info in metriques:
                    p_val = info.get('p_value', 1.0)
                    diff_moy = info.get('difference_moyennes', 0.0)
                    section.append(f"  {nom} : p-value={p_val:.6f}, diff_moyennes={diff_moy:.6f}")
                section.append("")

        return section

    def _generer_section_analyse_diff(self, donnees_analyse: List[Dict]) -> List[str]:
        """Génère la section d'analyse spéciale DIFF."""
        section = []
        section.append("3. ANALYSE SPÉCIALE VARIABLE DIFF")
        section.append("-" * 40)

        # Analyser DIFF par pattern
        donnees_s = [d for d in donnees_analyse if d.get('pattern') == 'S']
        donnees_o = [d for d in donnees_analyse if d.get('pattern') == 'O']

        diff_s = [d.get('diff', 0.0) for d in donnees_s if 'diff' in d]
        diff_o = [d.get('diff', 0.0) for d in donnees_o if 'diff' in d]

        if diff_s and diff_o:
            section.append("DIFF POUR PATTERNS S (continuation) :")
            section.append(f"  Moyenne : {np.mean(diff_s):.6f}")
            section.append(f"  Médiane : {np.median(diff_s):.6f}")
            section.append(f"  Écart-type : {np.std(diff_s):.6f}")
            section.append("")

            section.append("DIFF POUR PATTERNS O (alternance) :")
            section.append(f"  Moyenne : {np.mean(diff_o):.6f}")
            section.append(f"  Médiane : {np.median(diff_o):.6f}")
            section.append(f"  Écart-type : {np.std(diff_o):.6f}")
            section.append("")

            # Différence
            diff_moyennes = abs(np.mean(diff_s) - np.mean(diff_o))
            section.append(f"DIFFÉRENCE ABSOLUE DES MOYENNES : {diff_moyennes:.6f}")

            if diff_moyennes > 0.01:
                section.append("→ DIFFÉRENCE SIGNIFICATIVE détectée !")
            else:
                section.append("→ Différence faible entre S et O")

        section.append("")
        return section

    def _generer_section_recommandations(self, resultats_significance: Dict[str, Any]) -> List[str]:
        """Génère la section des recommandations."""
        section = []
        section.append("4. RECOMMANDATIONS PRÉDICTIVES")
        section.append("-" * 40)

        metriques_sig = resultats_significance.get('metriques_significatives', {})

        if not metriques_sig:
            section.append("• Aucune métrique significative → Prédiction difficile")
            section.append("• Recommandation : Analyser plus de données ou ajuster les seuils")
        else:
            section.append("MÉTRIQUES RECOMMANDÉES POUR PRÉDICTION :")

            # Top 3 des métriques les plus significatives
            metriques_triees = sorted(
                metriques_sig.items(),
                key=lambda x: x[1].get('p_value', 1.0)
            )[:3]

            for i, (nom, info) in enumerate(metriques_triees, 1):
                force = info.get('force', 'FAIBLE')
                section.append(f"{i}. {nom} (Force: {force})")

            section.append("")
            section.append("STRATÉGIE RECOMMANDÉE :")
            section.append("• Utiliser les métriques FORTE significance en priorité")
            section.append("• Combiner plusieurs métriques pour améliorer la précision")
            section.append("• Surveiller la variable DIFF comme indicateur de qualité")
            section.append("• Éviter les prédictions quand DIFF > 0.150 (signal douteux)")

        section.append("")
        section.append("=" * 80)
        return section


# ============================================================================
# FONCTION PRINCIPALE ET TESTS
# ============================================================================

def main():
    """Fonction principale pour tester l'analyseur."""

    dataset_path = "dataset_baccarat_lupasco_20250624_104837.json"

    print("LANCEMENT ANALYSEUR BACCARAT OPTIMISE - ANABCT.PY")
    print("=" * 80)

    # Créer l'analyseur
    analyseur = AnalyseurBaccaratOptimise(dataset_path)

    # Exécuter l'analyse complète
    resultats = analyseur.executer_analyse_complete()

    if 'erreur' in resultats:
        print(f"ERREUR : {resultats['erreur']}")
        return False

    # Afficher les statistiques finales
    stats = resultats.get('statistiques', {})
    print(f"\nANALYSE TERMINEE AVEC SUCCES")
    print(f"Parties traitees : {stats.get('parties_traitees', 0):,}")
    print(f"Mains analysees : {stats.get('mains_analysees', 0):,}")
    print(f"Metriques par main : {stats.get('metriques_par_main', 0):,}")

    # DEBUG : Compter les patterns S/O dans les données
    donnees_analyse = resultats.get('donnees_analyse', [])
    patterns_s = [d for d in donnees_analyse if d.get('pattern') == 'S']
    patterns_o = [d for d in donnees_analyse if d.get('pattern') == 'O']
    patterns_e = [d for d in donnees_analyse if d.get('pattern') == 'E']
    patterns_autres = [d for d in donnees_analyse if d.get('pattern') not in ['S', 'O', 'E']]

    print(f"\nDEBUG PATTERNS:")
    print(f"Patterns S: {len(patterns_s)}")
    print(f"Patterns O: {len(patterns_o)}")
    print(f"Patterns E: {len(patterns_e)}")
    print(f"Patterns autres: {len(patterns_autres)}")

    if patterns_autres:
        print(f"Exemples patterns autres: {[d.get('pattern') for d in patterns_autres[:5]]}")

    return True


if __name__ == "__main__":
    main()
